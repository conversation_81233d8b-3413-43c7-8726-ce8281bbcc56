#!/bin/bash

echo "Verifying Grafana deployment..."

# Wait for Grafana pods to be ready
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=grafana -n monitoring --timeout=300s

# Check pod status
echo "Pod status:"
kubectl get pods -l app.kubernetes.io/name=grafana -n monitoring

# Check pod logs
echo "Pod logs:"
kubectl logs -l app.kubernetes.io/name=grafana -n monitoring

# Check service status
echo "Service status:"
kubectl get svc -l app.kubernetes.io/name=grafana -n monitoring

# Verify Grafana is responding
echo "Testing Grafana endpoint..."
kubectl run -n monitoring curl --image=curlimages/curl -i --rm --restart=Never -- curl -s http://grafana/api/health

echo "Grafana deployment verification complete!"

exit 0