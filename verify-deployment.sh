#!/bin/bash

echo "Verifying Loki deployment..."

# Wait for Loki pods to be ready
kubectl wait --for=condition=ready pod -l app.kubernetes.io/name=loki -n monitoring --timeout=300s

# Check pod status
echo "Pod status:"
kubectl get pods -l app.kubernetes.io/name=loki -n monitoring

# Check pod logs
echo "Pod logs:"
kubectl logs -l app.kubernetes.io/name=loki -n monitoring

# Check service status
echo "Service status:"
kubectl get svc -l app.kubernetes.io/name=loki -n monitoring

# Verify Loki is responding
echo "Testing Loki endpoint..."
kubectl run -n monitoring curl --image=curlimages/curl -i --rm --restart=Never -- curl -s http://loki:3100/ready

exit 0 