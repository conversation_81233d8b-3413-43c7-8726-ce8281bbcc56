{"version": 4, "terraform_version": "1.12.1", "serial": 29, "lineage": "1d76c1a2-0e60-5f6e-6b1b-100b75616bc8", "outputs": {}, "resources": [], "check_results": [{"object_kind": "resource", "config_addr": "module.eks.module.self_managed_node_group.module.user_data.null_resource.validate_cluster_service_cidr", "status": "unknown", "objects": null}, {"object_kind": "resource", "config_addr": "module.eks.module.eks_managed_node_group.module.user_data.null_resource.validate_cluster_service_cidr", "status": "unknown", "objects": null}, {"object_kind": "var", "config_addr": "module.eks.module.self_managed_node_group.var.platform", "status": "unknown", "objects": null}]}