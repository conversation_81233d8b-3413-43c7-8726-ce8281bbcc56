{"version": 4, "terraform_version": "1.12.1", "serial": 290, "lineage": "9fef09ab-f232-f289-0086-7e97a5fd1a65", "outputs": {"cluster_certificate_authority_data": {"value": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURCVENDQWUyZ0F3SUJBZ0lJQWozT0o3Qm9IV0F3RFFZSktvWklodmNOQVFFTEJRQXdGVEVUTUJFR0ExVUUKQXhNS2EzVmlaWEp1WlhSbGN6QWVGdzB5TlRBMk1EUXhNREl6TkRSYUZ3MHpOVEEyTURJeE1ESTRORFJhTUJVeApFekFSQmdOVkJBTVRDbXQxWW1WeWJtVjBaWE13Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLCkFvSUJBUUNVOW5DcTczdHJXaGx3S3l4WG9HdjRHUk1raVk4R2c0ckMzUzBLL2VCOXowZFlCamNEdlhKODE0M2QKV1NPN3ZnNWVBZCtOMkpPYjNDbEZhaFJHTmFCc01TaUdjNnNDdkovY0xxMjlqV0hPOWhwd25CYlFjWGh3azk2bwp3enUxWXk4ckIya3lYVWp1bWtqeFJ0blFmdklJSGF1Qlc3Z1RLaE41SnVGK2RVNWVseFRCRFZ3V2hUYjBaaXhDCi80UzBUK3Zta0gvR0l0amNyRW9mRGtFbWZIM0RZdklXT1pncWt0QVZIcVNYMTI0VGprTHJGR3BaYlJVNkNmY1IKVitNQS9DNjZNRG9MdWFMYUp1STBGOStGdHhDWkFDYjJyU2dGYjMyV2c3bm5hRVI5dWdKbnVQamU2ZUdGV3JtTwpsVVBIV3orS1VNRzhMNkNiTTk5ZTJMMS91eFhMQWdNQkFBR2pXVEJYTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQCkJnTlZIUk1CQWY4RUJUQURBUUgvTUIwR0ExVWREZ1FXQkJTYm5MWm0rczIxSExHY3VnS2hRekRqdkVWbWhUQVYKQmdOVkhSRUVEakFNZ2dwcmRXSmxjbTVsZEdWek1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRQUVqRWRhSjlyUwpkSitKcGtkMk1NZTF2SlVranRnSG16RGFkTlVCVEZEVWFta2hjNW1PaGs3NVNjM3Q2UElxbWlkeG9Od2FIV3pxCnhjS0JzRE82M3Nnb2tHSFhUbys4OERMTUt3UHlpSG1DaWhPVEdiUlhORmpRZmpwOXVhRFJWR3NBKzN3OFhEVXYKTlk2c0dBaGU0dTZ2WVNYRUQrWHJ0VlZ6Ujl6bGl1U0hiL3BtYms1cWVPNkd3N0psZDR3Y3N2My93ZzZyV1NwLwozMktqdnFkVmIzTVJ1WDRyemZsSnRMVUhFWEYwYWJaK1dGanBPZjl5MUF5U1g3Tk9jNm9tQXR0L2QyQnJuWktlCmwwNXRTTC9QMFlKTkdtZnBoT29rK0R2SjZneHdlL0UzUHhjeVN4Q1IrUU8yN2FPOS92eHkydkZyaGxUVEJET0MKWkN5MWdmbnpRU0lNCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K", "type": "string"}, "cluster_endpoint": {"value": "https://BC79ED0BC3A47A644D565260098D7B0B.gr7.us-east-1.eks.amazonaws.com", "type": "string"}, "cluster_iam_role_arn": {"value": "arn:aws:iam::************:role/az-eks-cluster-cluster-20250604102327690200000001", "type": "string"}, "cluster_iam_role_name": {"value": "az-eks-cluster-cluster-20250604102327690200000001", "type": "string"}, "cluster_name": {"value": "az-eks-cluster", "type": "string"}, "cluster_oidc_issuer_url": {"value": "https://oidc.eks.us-east-1.amazonaws.com/id/BC79ED0BC3A47A644D565260098D7B0B", "type": "string"}, "cluster_primary_security_group_id": {"value": "sg-0d23dd552779a09ca", "type": "string"}, "cluster_security_group_id": {"value": "sg-0d04fbdcf27b855f7", "type": "string"}, "kubectl_config_command": {"value": "aws eks update-kubeconfig --name az-eks-cluster --region us-east-1", "type": "string"}, "log_queries": {"value": {"all_logs": "{namespace!=\"\"} |= \"\" | __error__ = \"\"", "info_logs": "{namespace!=\"\"} |= \"level=info\" or {namespace!=\"\"} |= \"severity=info\" or {namespace!=\"\"} |= \"INFO\""}, "type": ["object", {"all_logs": "string", "info_logs": "string"}]}}, "resources": [{"mode": "data", "type": "aws_ami", "name": "eks", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"architecture": "x86_64", "arn": "arn:aws:ec2:us-east-1::image/ami-0f2e4735b924be9d0", "block_device_mappings": [{"device_name": "/dev/xvda", "ebs": {"delete_on_termination": "true", "encrypted": "false", "iops": "0", "snapshot_id": "snap-0a1fa1e7339a1f9dc", "throughput": "0", "volume_initialization_rate": "0", "volume_size": "20", "volume_type": "gp2"}, "no_device": "", "virtual_name": ""}], "boot_mode": "", "creation_date": "2025-05-20T17:09:13.000Z", "deprecation_time": "2027-05-20T17:09:13.000Z", "description": "EKS Kubernetes Worker AMI with AmazonLinux2 image, (k8s: 1.32.3, containerd: 1.7.*)", "ena_support": true, "executable_users": null, "filter": [{"name": "name", "values": ["amazon-eks-node-1.32-v*"]}], "hypervisor": "xen", "id": "ami-0f2e4735b924be9d0", "image_id": "ami-0f2e4735b924be9d0", "image_location": "amazon/amazon-eks-node-1.32-v20250519", "image_owner_alias": "amazon", "image_type": "machine", "imds_support": "", "include_deprecated": false, "kernel_id": "", "last_launched_time": "", "most_recent": true, "name": "amazon-eks-node-1.32-v20250519", "name_regex": null, "owner_id": "602401143452", "owners": ["amazon"], "platform": "", "platform_details": "Linux/UNIX", "product_codes": [], "public": true, "ramdisk_id": "", "root_device_name": "/dev/xvda", "root_device_type": "ebs", "root_snapshot_id": "snap-0a1fa1e7339a1f9dc", "sriov_net_support": "simple", "state": "available", "state_reason": {"code": "UNSET", "message": "UNSET"}, "tags": {}, "timeouts": null, "tpm_support": "", "uefi_data": null, "usage_operation": "RunInstances", "virtualization_type": "hvm"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"mode": "data", "type": "aws_availability_zones", "name": "available", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"all_availability_zones": null, "exclude_names": null, "exclude_zone_ids": null, "filter": null, "group_names": ["us-east-1-zg-1"], "id": "us-east-1", "names": ["us-east-1a", "us-east-1b", "us-east-1c", "us-east-1d", "us-east-1e", "us-east-1f"], "state": "available", "timeouts": null, "zone_ids": ["use1-az1", "use1-az2", "use1-az4", "use1-az6", "use1-az3", "use1-az5"]}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"mode": "managed", "type": "aws_iam_role", "name": "eks_cluster", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/az-eks-cluster-cluster-role", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"eks.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-06-04T10:23:28Z", "description": "", "force_detach_policies": false, "id": "az-eks-cluster-cluster-role", "inline_policy": [], "managed_policy_arns": ["arn:aws:iam::aws:policy/AmazonEKSClusterPolicy", "arn:aws:iam::aws:policy/AmazonEKSServicePolicy"], "max_session_duration": 3600, "name": "az-eks-cluster-cluster-role", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"Environment": "dev", "Project": "eks-loki", "Terraform": "true"}, "tags_all": {"Environment": "dev", "Project": "eks-loki", "Terraform": "true"}, "unique_id": "AROATXF4JQPH5WNSXHNBA"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_iam_role", "name": "eks_node_group", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/az-eks-cluster-node-group-role", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"ec2.amazonaws.com\"}}],\"Version\":\"2012-10-17\"}", "create_date": "2025-06-04T10:23:28Z", "description": "", "force_detach_policies": false, "id": "az-eks-cluster-node-group-role", "inline_policy": [], "managed_policy_arns": ["arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly", "arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy", "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy"], "max_session_duration": 3600, "name": "az-eks-cluster-node-group-role", "name_prefix": "", "path": "/", "permissions_boundary": "", "tags": {"Environment": "dev", "Project": "eks-loki", "Terraform": "true"}, "tags_all": {"Environment": "dev", "Project": "eks-loki", "Terraform": "true"}, "unique_id": "AROATXF4JQPH3WWI7N3MZ"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "create_before_destroy": true}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "ecr_read_only", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "az-eks-cluster-node-group-role-20250604102330299400000007", "policy_arn": "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly", "role": "az-eks-cluster-node-group-role"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks_node_group"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "eks_cluster_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "az-eks-cluster-cluster-role-20250604102329970300000003", "policy_arn": "arn:aws:iam::aws:policy/AmazonEKSClusterPolicy", "role": "az-eks-cluster-cluster-role"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks_cluster"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "eks_cni_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "az-eks-cluster-node-group-role-20250604102330280900000006", "policy_arn": "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy", "role": "az-eks-cluster-node-group-role"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks_node_group"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "eks_service_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "az-eks-cluster-cluster-role-20250604102329978400000004", "policy_arn": "arn:aws:iam::aws:policy/AmazonEKSServicePolicy", "role": "az-eks-cluster-cluster-role"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks_cluster"]}]}, {"mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "eks_worker_node_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"id": "az-eks-cluster-node-group-role-20250604102330007100000005", "policy_arn": "arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy", "role": "az-eks-cluster-node-group-role"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks_node_group"]}]}, {"mode": "managed", "type": "kubernetes_namespace", "name": "monitoring", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": [{"schema_version": 0, "attributes": {"id": "monitoring", "metadata": [{"annotations": {}, "generate_name": "", "generation": 0, "labels": {}, "name": "monitoring", "resource_version": "16700", "uid": "1296d17f-9b08-40d0-a3f4-c90aa0eecc80"}], "timeouts": {"delete": "15m"}, "wait_for_default_service_account": false}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************", "dependencies": ["aws_iam_role.eks_cluster", "aws_iam_role.eks_node_group", "data.aws_availability_zones.available", "module.eks.aws_cloudwatch_log_group.this", "module.eks.aws_ec2_tag.cluster_primary_security_group", "module.eks.aws_eks_addon.before_compute", "module.eks.aws_eks_addon.this", "module.eks.aws_eks_cluster.this", "module.eks.aws_eks_identity_provider_config.this", "module.eks.aws_iam_openid_connect_provider.oidc_provider", "module.eks.aws_iam_policy.cluster_encryption", "module.eks.aws_iam_policy.cni_ipv6_policy", "module.eks.aws_iam_role.this", "module.eks.aws_iam_role_policy_attachment.additional", "module.eks.aws_iam_role_policy_attachment.cluster_encryption", "module.eks.aws_iam_role_policy_attachment.this", "module.eks.aws_security_group.cluster", "module.eks.aws_security_group.node", "module.eks.aws_security_group_rule.cluster", "module.eks.aws_security_group_rule.node", "module.eks.data.aws_caller_identity.current", "module.eks.data.aws_eks_addon_version.this", "module.eks.data.aws_iam_policy_document.assume_role_policy", "module.eks.data.aws_iam_policy_document.cni_ipv6_policy", "module.eks.data.aws_iam_session_context.current", "module.eks.data.aws_partition.current", "module.eks.data.tls_certificate.this", "module.eks.kubernetes_config_map.aws_auth", "module.eks.kubernetes_config_map_v1_data.aws_auth", "module.eks.module.eks_managed_node_group.aws_autoscaling_schedule.this", "module.eks.module.eks_managed_node_group.aws_eks_node_group.this", "module.eks.module.eks_managed_node_group.aws_iam_role.this", "module.eks.module.eks_managed_node_group.aws_iam_role_policy_attachment.additional", "module.eks.module.eks_managed_node_group.aws_iam_role_policy_attachment.this", "module.eks.module.eks_managed_node_group.aws_launch_template.this", "module.eks.module.eks_managed_node_group.data.aws_caller_identity.current", "module.eks.module.eks_managed_node_group.data.aws_iam_policy_document.assume_role_policy", "module.eks.module.eks_managed_node_group.data.aws_partition.current", "module.eks.module.eks_managed_node_group.module.user_data.data.cloudinit_config.linux_eks_managed_node_group", "module.eks.module.fargate_profile.aws_eks_fargate_profile.this", "module.eks.module.fargate_profile.aws_iam_role.this", "module.eks.module.fargate_profile.aws_iam_role_policy_attachment.additional", "module.eks.module.fargate_profile.aws_iam_role_policy_attachment.this", "module.eks.module.fargate_profile.data.aws_caller_identity.current", "module.eks.module.fargate_profile.data.aws_iam_policy_document.assume_role_policy", "module.eks.module.fargate_profile.data.aws_partition.current", "module.eks.module.kms.aws_kms_alias.this", "module.eks.module.kms.aws_kms_external_key.this", "module.eks.module.kms.aws_kms_grant.this", "module.eks.module.kms.aws_kms_key.this", "module.eks.module.kms.aws_kms_replica_external_key.this", "module.eks.module.kms.aws_kms_replica_key.this", "module.eks.module.kms.data.aws_caller_identity.current", "module.eks.module.kms.data.aws_iam_policy_document.this", "module.eks.module.kms.data.aws_partition.current", "module.eks.module.self_managed_node_group.aws_autoscaling_group.this", "module.eks.module.self_managed_node_group.aws_autoscaling_schedule.this", "module.eks.module.self_managed_node_group.aws_iam_instance_profile.this", "module.eks.module.self_managed_node_group.aws_iam_role.this", "module.eks.module.self_managed_node_group.aws_iam_role_policy_attachment.additional", "module.eks.module.self_managed_node_group.aws_iam_role_policy_attachment.this", "module.eks.module.self_managed_node_group.aws_launch_template.this", "module.eks.module.self_managed_node_group.data.aws_ami.eks_default", "module.eks.module.self_managed_node_group.data.aws_caller_identity.current", "module.eks.module.self_managed_node_group.data.aws_iam_policy_document.assume_role_policy", "module.eks.module.self_managed_node_group.data.aws_partition.current", "module.eks.module.self_managed_node_group.module.user_data.data.cloudinit_config.linux_eks_managed_node_group", "module.eks.time_sleep.this", "module.vpc.aws_subnet.private", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this", "null_resource.wait_for_cluster"]}]}, {"mode": "managed", "type": "null_resource", "name": "wait_for_cluster", "provider": "provider[\"registry.terraform.io/hashicorp/null\"]", "instances": [{"schema_version": 0, "attributes": {"id": "989314734054874402", "triggers": null}, "sensitive_attributes": [], "identity_schema_version": 0, "dependencies": ["aws_iam_role.eks_cluster", "aws_iam_role.eks_node_group", "data.aws_availability_zones.available", "module.eks.aws_cloudwatch_log_group.this", "module.eks.aws_ec2_tag.cluster_primary_security_group", "module.eks.aws_eks_addon.before_compute", "module.eks.aws_eks_addon.this", "module.eks.aws_eks_cluster.this", "module.eks.aws_eks_identity_provider_config.this", "module.eks.aws_iam_openid_connect_provider.oidc_provider", "module.eks.aws_iam_policy.cluster_encryption", "module.eks.aws_iam_policy.cni_ipv6_policy", "module.eks.aws_iam_role.this", "module.eks.aws_iam_role_policy_attachment.additional", "module.eks.aws_iam_role_policy_attachment.cluster_encryption", "module.eks.aws_iam_role_policy_attachment.this", "module.eks.aws_security_group.cluster", "module.eks.aws_security_group.node", "module.eks.aws_security_group_rule.cluster", "module.eks.aws_security_group_rule.node", "module.eks.data.aws_caller_identity.current", "module.eks.data.aws_eks_addon_version.this", "module.eks.data.aws_iam_policy_document.assume_role_policy", "module.eks.data.aws_iam_policy_document.cni_ipv6_policy", "module.eks.data.aws_iam_session_context.current", "module.eks.data.aws_partition.current", "module.eks.data.tls_certificate.this", "module.eks.kubernetes_config_map.aws_auth", "module.eks.kubernetes_config_map_v1_data.aws_auth", "module.eks.module.eks_managed_node_group.aws_autoscaling_schedule.this", "module.eks.module.eks_managed_node_group.aws_eks_node_group.this", "module.eks.module.eks_managed_node_group.aws_iam_role.this", "module.eks.module.eks_managed_node_group.aws_iam_role_policy_attachment.additional", "module.eks.module.eks_managed_node_group.aws_iam_role_policy_attachment.this", "module.eks.module.eks_managed_node_group.aws_launch_template.this", "module.eks.module.eks_managed_node_group.data.aws_caller_identity.current", "module.eks.module.eks_managed_node_group.data.aws_iam_policy_document.assume_role_policy", "module.eks.module.eks_managed_node_group.data.aws_partition.current", "module.eks.module.eks_managed_node_group.module.user_data.data.cloudinit_config.linux_eks_managed_node_group", "module.eks.module.fargate_profile.aws_eks_fargate_profile.this", "module.eks.module.fargate_profile.aws_iam_role.this", "module.eks.module.fargate_profile.aws_iam_role_policy_attachment.additional", "module.eks.module.fargate_profile.aws_iam_role_policy_attachment.this", "module.eks.module.fargate_profile.data.aws_caller_identity.current", "module.eks.module.fargate_profile.data.aws_iam_policy_document.assume_role_policy", "module.eks.module.fargate_profile.data.aws_partition.current", "module.eks.module.kms.aws_kms_alias.this", "module.eks.module.kms.aws_kms_external_key.this", "module.eks.module.kms.aws_kms_grant.this", "module.eks.module.kms.aws_kms_key.this", "module.eks.module.kms.aws_kms_replica_external_key.this", "module.eks.module.kms.aws_kms_replica_key.this", "module.eks.module.kms.data.aws_caller_identity.current", "module.eks.module.kms.data.aws_iam_policy_document.this", "module.eks.module.kms.data.aws_partition.current", "module.eks.module.self_managed_node_group.aws_autoscaling_group.this", "module.eks.module.self_managed_node_group.aws_autoscaling_schedule.this", "module.eks.module.self_managed_node_group.aws_iam_instance_profile.this", "module.eks.module.self_managed_node_group.aws_iam_role.this", "module.eks.module.self_managed_node_group.aws_iam_role_policy_attachment.additional", "module.eks.module.self_managed_node_group.aws_iam_role_policy_attachment.this", "module.eks.module.self_managed_node_group.aws_launch_template.this", "module.eks.module.self_managed_node_group.data.aws_ami.eks_default", "module.eks.module.self_managed_node_group.data.aws_caller_identity.current", "module.eks.module.self_managed_node_group.data.aws_iam_policy_document.assume_role_policy", "module.eks.module.self_managed_node_group.data.aws_partition.current", "module.eks.module.self_managed_node_group.module.user_data.data.cloudinit_config.linux_eks_managed_node_group", "module.eks.time_sleep.this", "module.vpc.aws_subnet.private", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"]}]}, {"mode": "managed", "type": "null_resource", "name": "wait_for_cluster_ready", "provider": "provider[\"registry.terraform.io/hashicorp/null\"]", "instances": [{"schema_version": 0, "attributes": {"id": "7320679704652070810", "triggers": null}, "sensitive_attributes": [], "identity_schema_version": 0, "dependencies": ["aws_iam_role.eks_cluster", "aws_iam_role.eks_node_group", "data.aws_availability_zones.available", "module.eks.aws_cloudwatch_log_group.this", "module.eks.aws_ec2_tag.cluster_primary_security_group", "module.eks.aws_eks_addon.before_compute", "module.eks.aws_eks_addon.this", "module.eks.aws_eks_cluster.this", "module.eks.aws_eks_identity_provider_config.this", "module.eks.aws_iam_openid_connect_provider.oidc_provider", "module.eks.aws_iam_policy.cluster_encryption", "module.eks.aws_iam_policy.cni_ipv6_policy", "module.eks.aws_iam_role.this", "module.eks.aws_iam_role_policy_attachment.additional", "module.eks.aws_iam_role_policy_attachment.cluster_encryption", "module.eks.aws_iam_role_policy_attachment.this", "module.eks.aws_security_group.cluster", "module.eks.aws_security_group.node", "module.eks.aws_security_group_rule.cluster", "module.eks.aws_security_group_rule.node", "module.eks.data.aws_caller_identity.current", "module.eks.data.aws_eks_addon_version.this", "module.eks.data.aws_iam_policy_document.assume_role_policy", "module.eks.data.aws_iam_policy_document.cni_ipv6_policy", "module.eks.data.aws_iam_session_context.current", "module.eks.data.aws_partition.current", "module.eks.data.tls_certificate.this", "module.eks.kubernetes_config_map.aws_auth", "module.eks.kubernetes_config_map_v1_data.aws_auth", "module.eks.module.eks_managed_node_group.aws_autoscaling_schedule.this", "module.eks.module.eks_managed_node_group.aws_eks_node_group.this", "module.eks.module.eks_managed_node_group.aws_iam_role.this", "module.eks.module.eks_managed_node_group.aws_iam_role_policy_attachment.additional", "module.eks.module.eks_managed_node_group.aws_iam_role_policy_attachment.this", "module.eks.module.eks_managed_node_group.aws_launch_template.this", "module.eks.module.eks_managed_node_group.data.aws_caller_identity.current", "module.eks.module.eks_managed_node_group.data.aws_iam_policy_document.assume_role_policy", "module.eks.module.eks_managed_node_group.data.aws_partition.current", "module.eks.module.eks_managed_node_group.module.user_data.data.cloudinit_config.linux_eks_managed_node_group", "module.eks.module.fargate_profile.aws_eks_fargate_profile.this", "module.eks.module.fargate_profile.aws_iam_role.this", "module.eks.module.fargate_profile.aws_iam_role_policy_attachment.additional", "module.eks.module.fargate_profile.aws_iam_role_policy_attachment.this", "module.eks.module.fargate_profile.data.aws_caller_identity.current", "module.eks.module.fargate_profile.data.aws_iam_policy_document.assume_role_policy", "module.eks.module.fargate_profile.data.aws_partition.current", "module.eks.module.kms.aws_kms_alias.this", "module.eks.module.kms.aws_kms_external_key.this", "module.eks.module.kms.aws_kms_grant.this", "module.eks.module.kms.aws_kms_key.this", "module.eks.module.kms.aws_kms_replica_external_key.this", "module.eks.module.kms.aws_kms_replica_key.this", "module.eks.module.kms.data.aws_caller_identity.current", "module.eks.module.kms.data.aws_iam_policy_document.this", "module.eks.module.kms.data.aws_partition.current", "module.eks.module.self_managed_node_group.aws_autoscaling_group.this", "module.eks.module.self_managed_node_group.aws_autoscaling_schedule.this", "module.eks.module.self_managed_node_group.aws_iam_instance_profile.this", "module.eks.module.self_managed_node_group.aws_iam_role.this", "module.eks.module.self_managed_node_group.aws_iam_role_policy_attachment.additional", "module.eks.module.self_managed_node_group.aws_iam_role_policy_attachment.this", "module.eks.module.self_managed_node_group.aws_launch_template.this", "module.eks.module.self_managed_node_group.data.aws_ami.eks_default", "module.eks.module.self_managed_node_group.data.aws_caller_identity.current", "module.eks.module.self_managed_node_group.data.aws_iam_policy_document.assume_role_policy", "module.eks.module.self_managed_node_group.data.aws_partition.current", "module.eks.module.self_managed_node_group.module.user_data.data.cloudinit_config.linux_eks_managed_node_group", "module.eks.time_sleep.this", "module.vpc.aws_subnet.private", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this", "null_resource.wait_for_cluster"]}]}, {"module": "module.eks", "mode": "data", "type": "aws_caller_identity", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "************", "arn": "arn:aws:iam::************:user/azni_ce9", "id": "************", "user_id": "AIDATXF4JQPHQNHQA2SKB"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.eks", "mode": "data", "type": "aws_eks_addon_version", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.eks", "mode": "data", "type": "aws_iam_policy_document", "name": "assume_role_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Sid\": \"EKSClusterAssumeRole\",\n      \"Effect\": \"Allow\",\n      \"Action\": \"sts:AssumeRole\",\n      \"Principal\": {\n        \"Service\": \"eks.amazonaws.com\"\n      }\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Sid\":\"EKSClusterAssumeRole\",\"Effect\":\"Allow\",\"Action\":\"sts:AssumeRole\",\"Principal\":{\"Service\":\"eks.amazonaws.com\"}}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["sts:<PERSON><PERSON>Role"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["eks.amazonaws.com"], "type": "Service"}], "resources": [], "sid": "EKSClusterAssumeRole"}], "version": "2012-10-17"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.eks", "mode": "data", "type": "aws_iam_policy_document", "name": "cni_ipv6_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.eks", "mode": "data", "type": "aws_iam_session_context", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:user/azni_ce9", "id": "arn:aws:iam::************:user/azni_ce9", "issuer_arn": "arn:aws:iam::************:user/azni_ce9", "issuer_id": "", "issuer_name": "", "session_name": ""}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.eks", "mode": "data", "type": "aws_partition", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"dns_suffix": "amazonaws.com", "id": "aws", "partition": "aws", "reverse_dns_prefix": "com.amazonaws"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.eks", "mode": "data", "type": "tls_certificate", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/tls\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"certificates": [{"cert_pem": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIIEdTCCA12gAwIBAgIJAKcOSkw0grd/MA0GCSqGSIb3DQEBCwUAMGgxCzAJBgNV\nBAYTAlVTMSUwIwYDVQQKExxTdGFyZmllbGQgVGVjaG5vbG9naWVzLCBJbmMuMTIw\nMAYDVQQLEylTdGFyZmllbGQgQ2xhc3MgMiBDZXJ0aWZpY2F0aW9uIEF1dGhvcml0\neTAeFw0wOTA5MDIwMDAwMDBaFw0zNDA2MjgxNzM5MTZaMIGYMQswCQYDVQQGEwJV\nUzEQMA4GA1UECBMHQXJpem9uYTETMBEGA1UEBxMKU2NvdHRzZGFsZTElMCMGA1UE\nChMcU3RhcmZpZWxkIFRlY2hub2xvZ2llcywgSW5jLjE7MDkGA1UEAxMyU3RhcmZp\nZWxkIFNlcnZpY2VzIFJvb3QgQ2VydGlmaWNhdGUgQXV0aG9yaXR5IC0gRzIwggEi\nMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIBAQDVDDrEKvlO4vW+GZdfjohTsR8/\ny8+fIBNtKTrID30892t2OGPZNmCom15cAICyL1l/9of5JUOG52kbUpqQ4XHj2C0N\nTm/2yEnZtvMaVq4rtnQU68/7JuMauh2WLmo7WJSJR1b/JaCTcFOD2oR0FMNnngRo\nOt+OQFodSk7PQ5E751bWAHDLUu57fa4657wx+UX2wmDPE1kCK4DMNEffud6QZW0C\nzyyRpqbn3oUYSXxmTqM6bam17jQuug0DuDPfR+uxa40l2ZvOgdFFRjKWcIfeAg5J\nQ4W2bHO7ZOphQazJ1FTfhy/HIrImzJ9ZVGif/L4qL8RVHHVAYBeFAlU5i38FAgMB\nAAGjgfAwge0wDwYDVR0TAQH/BAUwAwEB/zAOBgNVHQ8BAf8EBAMCAYYwHQYDVR0O\nBBYEFJxfAN+qAdcwKziIorhtSpzyEZGDMB8GA1UdIwQYMBaAFL9ft9HO3R+G9FtV\nrNzXEMIOqYjnME8GCCsGAQUFBwEBBEMwQTAcBggrBgEFBQcwAYYQaHR0cDovL28u\nc3MyLnVzLzAhBggrBgEFBQcwAoYVaHR0cDovL3guc3MyLnVzL3guY2VyMCYGA1Ud\nHwQfMB0wG6AZoBeGFWh0dHA6Ly9zLnNzMi51cy9yLmNybDARBgNVHSAECjAIMAYG\nBFUdIAAwDQYJKoZIhvcNAQELBQADggEBACMd44pXyn3pF3lM8R5V/cxTbj5HD9/G\nVfKyBDbtgB9TxF00KGu+x1X8Z+rLP3+QsjPNG1gQggL4+C/1E2DUBc7xgQjB3ad1\nl08YuW3e95ORCLp+QCztweq7dp4zBncdDQh/U90bZKuCJ/Fp1U1ervShw3WnWEQt\n8jxwmKy6abaVd38PMV4s/KCHOkdp8Hlf9BRUpJVeEXgSYCfOn8J3/yNTd126/+pZ\n59vPr5KW7ySaNRB6nJHGDn2Z9j8Z3/VyVOEVqQdZe4O/Ui5GjLIAZHYcSNPYeehu\nVsyuLAOQ1xk4meTKCRlb/weWsKh/NEnfVqn3sF/tM+2MR7cwA130A4w=\n-----END CERTIFICATE-----\n", "is_ca": true, "issuer": "OU=Starfield Class 2 Certification Authority,O=Starfield Technologies\\, Inc.,C=US", "not_after": "2034-06-28T17:39:16Z", "not_before": "2009-09-02T00:00:00Z", "public_key_algorithm": "RSA", "serial_number": "12037640545166866303", "sha1_fingerprint": "9e99a48a9960b14926bb7f3b02e22da2b0ab7280", "signature_algorithm": "SHA256-RSA", "subject": "CN=Starfield Services Root Certificate Authority - G2,O=Starfield Technologies\\, Inc.,L=Scottsdale,ST=Arizona,C=US", "version": 3}, {"cert_pem": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIIEkjCCA3qgAwIBAgITBn+USionzfP6wq4rAfkI7rnExjANBgkqhkiG9w0BAQsF\nADCBmDELMAkGA1UEBhMCVVMxEDAOBgNVBAgTB0FyaXpvbmExEzARBgNVBAcTClNj\nb3R0c2RhbGUxJTAjBgNVBAoTHFN0YXJmaWVsZCBUZWNobm9sb2dpZXMsIEluYy4x\nOzA5BgNVBAMTMlN0YXJmaWVsZCBTZXJ2aWNlcyBSb290IENlcnRpZmljYXRlIEF1\ndGhvcml0eSAtIEcyMB4XDTE1MDUyNTEyMDAwMFoXDTM3MTIzMTAxMDAwMFowOTEL\nMAkGA1UEBhMCVVMxDzANBgNVBAoTBkFtYXpvbjEZMBcGA1UEAxMQQW1hem9uIFJv\nb3QgQ0EgMTCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALJ4gHHKeNXj\nca9HgFB0fW7Y14h29Jlo91ghYPl0hAEvrAIthtOgQ3pOsqTQNroBvo3bSMgHFzZM\n9O6II8c+6zf1tRn4SWiw3te5djgdYZ6k/oI2peVKVuRF4fn9tBb6dNqcmzU5L/qw\nIFAGbHrQgLKm+a/sRxmPUDgH3KKHOVj4utWp+UhnMJbulHheb4mjUcAwhmahRWa6\nVOujw5H5SNz/0egwLX0tdHA114gk957EWW67c4cX8jJGKLhD+rcdqsq08p8kDi1L\n93FcXmn/6pUCyziKrlA4b9v7LWIbxcceVOF34GfID5yHI9Y/QCB/IIDEgEw+OyQm\njgSubJrIqg0CAwEAAaOCATEwggEtMA8GA1UdEwEB/wQFMAMBAf8wDgYDVR0PAQH/\nBAQDAgGGMB0GA1UdDgQWBBSEGMyFNOy8DJSULghZnMeyEE4KCDAfBgNVHSMEGDAW\ngBScXwDfqgHXMCs4iKK4bUqc8hGRgzB4BggrBgEFBQcBAQRsMGowLgYIKwYBBQUH\nMAGGImh0dHA6Ly9vY3NwLnJvb3RnMi5hbWF6b250cnVzdC5jb20wOAYIKwYBBQUH\nMAKGLGh0dHA6Ly9jcnQucm9vdGcyLmFtYXpvbnRydXN0LmNvbS9yb290ZzIuY2Vy\nMD0GA1UdHwQ2MDQwMqAwoC6GLGh0dHA6Ly9jcmwucm9vdGcyLmFtYXpvbnRydXN0\nLmNvbS9yb290ZzIuY3JsMBEGA1UdIAQKMAgwBgYEVR0gADANBgkqhkiG9w0BAQsF\nAAOCAQEAYjdCXLwQtT6LLOkMm2xF4gcAevnFWAu5CIw+7bMlPLVvUOTNNWqnkzSW\nMiGpSESrnO09tKpzbeR/FoCJbM8oAxiDR3mjEH4wW6w7sGDgd9QIpuEdfF7Au/ma\neyKdpwAJfqxGF4PcnCZXmTA5YpaP7dreqsXMGz7KQ2hsVxa81Q4gLv7/wmpdLqBK\nbRRYh5TmOTFffHPLkIhqhBGWJ6bt2YFGpn6jcgAKUj6DiAdjd4lpFw85hdKrCEVN\n0FE6/V1dN2RMfjCyVSRCnTawXZwXgWHxyvkQAiSr6w10kY17RSlQOYiypok1JR4U\nakcjMS9cmvqtmg5iUaQqqcT5NJ0hGA==\n-----END CERTIFICATE-----\n", "is_ca": true, "issuer": "CN=Starfield Services Root Certificate Authority - G2,O=Starfield Technologies\\, Inc.,L=Scottsdale,ST=Arizona,C=US", "not_after": "2037-12-31T01:00:00Z", "not_before": "2015-05-25T12:00:00Z", "public_key_algorithm": "RSA", "serial_number": "144918191876577076464031512351042010504348870", "sha1_fingerprint": "06b25927c42a721631c1efd9431e648fa62e1e39", "signature_algorithm": "SHA256-RSA", "subject": "CN=Amazon Root CA 1,O=Amazon,C=US", "version": 3}, {"cert_pem": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----\nMIIEXjCCA0agAwIBAgITB3MSTNQG0mfAmRzdKZqfODF5hTANBgkqhkiG9w0BAQsF\nADA5MQswCQYDVQQGEwJVUzEPMA0GA1UEChMGQW1hem9uMRkwFwYDVQQDExBBbWF6\nb24gUm9vdCBDQSAxMB4XDTIyMDgyMzIyMjYwNFoXDTMwMDgyMzIyMjYwNFowPDEL\nMAkGA1UEBhMCVVMxDzANBgNVBAoTBkFtYXpvbjEcMBoGA1UEAxMTQW1hem9uIFJT\nQSAyMDQ4IE0wMzCCASIwDQYJKoZIhvcNAQEBBQADggEPADCCAQoCggEBALd/pVko\n8vuM475Tf45HV3BbCl/B9Jy89G1CRkFjcPY06WA9lS+7dWbUA7GtWUKoksr69hKM\nwcMsNpxlw7b3jeXFgxB09/nmalcAWtnLzF+LaDKEA5DQmvKzuh1nfIfqEiKCQSmX\nXh09Xs+dO7cm5qbaL2hhNJCSAejciwcvOFgFNgEMR42wm6KIFHsQW28jhA+1u/M0\np6fVwReuEgZfLfdx82Px0LJck3lST3EB/JfbdsdOzzzg5YkY1dfuqf8y5fUeZ7Cz\nWXbTjujwX/TovmeWKA36VLCz75azW6tDNuDn66FOpADZZ9omVaF6BqNJiLMVl6P3\n/c0OiUMC6Z5OfKcCAwEAAaOCAVowggFWMBIGA1UdEwEB/wQIMAYBAf8CAQAwDgYD\nVR0PAQH/BAQDAgGGMB0GA1UdJQQWMBQGCCsGAQUFBwMBBggrBgEFBQcDAjAdBgNV\nHQ4EFgQUVdkYX9IczAHhWLS+q9lVQgHXLgIwHwYDVR0jBBgwFoAUhBjMhTTsvAyU\nlC4IWZzHshBOCggwewYIKwYBBQUHAQEEbzBtMC8GCCsGAQUFBzABhiNodHRwOi8v\nb2NzcC5yb290Y2ExLmFtYXpvbnRydXN0LmNvbTA6BggrBgEFBQcwAoYuaHR0cDov\nL2NydC5yb290Y2ExLmFtYXpvbnRydXN0LmNvbS9yb290Y2ExLmNlcjA/BgNVHR8E\nODA2MDSgMqAwhi5odHRwOi8vY3JsLnJvb3RjYTEuYW1hem9udHJ1c3QuY29tL3Jv\nb3RjYTEuY3JsMBMGA1UdIAQMMAowCAYGZ4EMAQIBMA0GCSqGSIb3DQEBCwUAA4IB\nAQAGjeWm2cC+3z2MzSCnte46/7JZvj3iQZDY7EvODNdZF41n71Lrk9kbfNwerK0d\nVNzW36Wefr7j7ZSwBVg50W5ay65jNSN74TTQV1yt4WnSbVvN6KlMs1hiyOZdoHKs\nKDV2UGNxbdoBYCQNa2GYF8FQIWLugNp35aSOpMy6cFlymFQomIrnOQHwK1nvVY4q\nxDSJMU/gNJz17D8ArPN3ngnyZ2TwepJ0uBINz3G5te2rdFUF4i4Y3Bb7FUlHDYm4\nu8aIRGpk2ZpfXmxaoxnbIBZRvGLPSUuPwnwoUOMsJ8jirI5vs2dvchPb7MtI1rle\ni02f2ivH2vxkjDLltSpe2fiC\n-----END CERTIFICATE-----\n", "is_ca": true, "issuer": "CN=Amazon Root CA 1,O=Amazon,C=US", "not_after": "2030-08-23T22:26:04Z", "not_before": "2022-08-23T22:26:04Z", "public_key_algorithm": "RSA", "serial_number": "166129356476704345391986644047206454194698629", "sha1_fingerprint": "d9fe0a65fa00cabf61f5120d373a8135e1461f15", "signature_algorithm": "SHA256-RSA", "subject": "CN=Amazon RSA 2048 M03,O=Amazon,C=US", "version": 3}, {"cert_pem": "-----<PERSON><PERSON><PERSON> CERTIFICATE-----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\n0mKQwrjkiglpYaXc247Qi8IUCdk6s13xHJ4e\n-----END CERTIFICATE-----\n", "is_ca": false, "issuer": "CN=Amazon RSA 2048 M03,O=Amazon,C=US", "not_after": "2026-03-27T23:59:59Z", "not_before": "2025-02-26T00:00:00Z", "public_key_algorithm": "RSA", "serial_number": "1038333404564743245365640370**********", "sha1_fingerprint": "63462dda480d8b900e0a7dbfaf6238a62ba4fce0", "signature_algorithm": "SHA256-RSA", "subject": "CN=*.eks.us-east-1.amazonaws.com", "version": 3}], "content": null, "id": "922877a0975ad078a65b8ff11ebc47b8311945c7", "url": "https://oidc.eks.us-east-1.amazonaws.com/id/BC79ED0BC3A47A644D565260098D7B0B", "verify_chain": true}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.eks", "mode": "managed", "type": "aws_cloudwatch_log_group", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:logs:us-east-1:************:log-group:/aws/eks/az-eks-cluster/cluster", "id": "/aws/eks/az-eks-cluster/cluster", "kms_key_id": "", "log_group_class": "STANDARD", "name": "/aws/eks/az-eks-cluster/cluster", "name_prefix": "", "retention_in_days": 90, "skip_destroy": false, "tags": {"Environment": "dev", "Name": "/aws/eks/az-eks-cluster/cluster", "Project": "eks-loki", "Terraform": "true"}, "tags_all": {"Environment": "dev", "Name": "/aws/eks/az-eks-cluster/cluster", "Project": "eks-loki", "Terraform": "true"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "create_before_destroy": true}]}, {"module": "module.eks", "mode": "managed", "type": "aws_ec2_tag", "name": "cluster_primary_security_group", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "Environment", "schema_version": 0, "attributes": {"id": "sg-0d23dd552779a09ca,Environment", "key": "Environment", "resource_id": "sg-0d23dd552779a09ca", "value": "dev"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks_cluster", "data.aws_availability_zones.available", "module.eks.aws_cloudwatch_log_group.this", "module.eks.aws_eks_cluster.this", "module.eks.aws_iam_policy.cni_ipv6_policy", "module.eks.aws_iam_role.this", "module.eks.aws_iam_role_policy_attachment.this", "module.eks.aws_security_group.cluster", "module.eks.aws_security_group.node", "module.eks.aws_security_group_rule.cluster", "module.eks.aws_security_group_rule.node", "module.eks.data.aws_caller_identity.current", "module.eks.data.aws_iam_policy_document.assume_role_policy", "module.eks.data.aws_iam_policy_document.cni_ipv6_policy", "module.eks.data.aws_iam_session_context.current", "module.eks.data.aws_partition.current", "module.eks.module.kms.aws_kms_external_key.this", "module.eks.module.kms.aws_kms_key.this", "module.eks.module.kms.aws_kms_replica_external_key.this", "module.eks.module.kms.aws_kms_replica_key.this", "module.eks.module.kms.data.aws_caller_identity.current", "module.eks.module.kms.data.aws_iam_policy_document.this", "module.eks.module.kms.data.aws_partition.current", "module.vpc.aws_subnet.private", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"]}, {"index_key": "Project", "schema_version": 0, "attributes": {"id": "sg-0d23dd552779a09ca,Project", "key": "Project", "resource_id": "sg-0d23dd552779a09ca", "value": "eks-loki"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks_cluster", "data.aws_availability_zones.available", "module.eks.aws_cloudwatch_log_group.this", "module.eks.aws_eks_cluster.this", "module.eks.aws_iam_policy.cni_ipv6_policy", "module.eks.aws_iam_role.this", "module.eks.aws_iam_role_policy_attachment.this", "module.eks.aws_security_group.cluster", "module.eks.aws_security_group.node", "module.eks.aws_security_group_rule.cluster", "module.eks.aws_security_group_rule.node", "module.eks.data.aws_caller_identity.current", "module.eks.data.aws_iam_policy_document.assume_role_policy", "module.eks.data.aws_iam_policy_document.cni_ipv6_policy", "module.eks.data.aws_iam_session_context.current", "module.eks.data.aws_partition.current", "module.eks.module.kms.aws_kms_external_key.this", "module.eks.module.kms.aws_kms_key.this", "module.eks.module.kms.aws_kms_replica_external_key.this", "module.eks.module.kms.aws_kms_replica_key.this", "module.eks.module.kms.data.aws_caller_identity.current", "module.eks.module.kms.data.aws_iam_policy_document.this", "module.eks.module.kms.data.aws_partition.current", "module.vpc.aws_subnet.private", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"]}, {"index_key": "Terraform", "schema_version": 0, "attributes": {"id": "sg-0d23dd552779a09ca,Terraform", "key": "Terraform", "resource_id": "sg-0d23dd552779a09ca", "value": "true"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks_cluster", "data.aws_availability_zones.available", "module.eks.aws_cloudwatch_log_group.this", "module.eks.aws_eks_cluster.this", "module.eks.aws_iam_policy.cni_ipv6_policy", "module.eks.aws_iam_role.this", "module.eks.aws_iam_role_policy_attachment.this", "module.eks.aws_security_group.cluster", "module.eks.aws_security_group.node", "module.eks.aws_security_group_rule.cluster", "module.eks.aws_security_group_rule.node", "module.eks.data.aws_caller_identity.current", "module.eks.data.aws_iam_policy_document.assume_role_policy", "module.eks.data.aws_iam_policy_document.cni_ipv6_policy", "module.eks.data.aws_iam_session_context.current", "module.eks.data.aws_partition.current", "module.eks.module.kms.aws_kms_external_key.this", "module.eks.module.kms.aws_kms_key.this", "module.eks.module.kms.aws_kms_replica_external_key.this", "module.eks.module.kms.aws_kms_replica_key.this", "module.eks.module.kms.data.aws_caller_identity.current", "module.eks.module.kms.data.aws_iam_policy_document.this", "module.eks.module.kms.data.aws_partition.current", "module.vpc.aws_subnet.private", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"]}]}, {"module": "module.eks", "mode": "managed", "type": "aws_eks_addon", "name": "before_compute", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.eks", "mode": "managed", "type": "aws_eks_addon", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.eks", "mode": "managed", "type": "aws_eks_cluster", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"access_config": [{"authentication_mode": "CONFIG_MAP", "bootstrap_cluster_creator_admin_permissions": true}], "arn": "arn:aws:eks:us-east-1:************:cluster/az-eks-cluster", "bootstrap_self_managed_addons": true, "certificate_authority": [{"data": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURCVENDQWUyZ0F3SUJBZ0lJQWozT0o3Qm9IV0F3RFFZSktvWklodmNOQVFFTEJRQXdGVEVUTUJFR0ExVUUKQXhNS2EzVmlaWEp1WlhSbGN6QWVGdzB5TlRBMk1EUXhNREl6TkRSYUZ3MHpOVEEyTURJeE1ESTRORFJhTUJVeApFekFSQmdOVkJBTVRDbXQxWW1WeWJtVjBaWE13Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLCkFvSUJBUUNVOW5DcTczdHJXaGx3S3l4WG9HdjRHUk1raVk4R2c0ckMzUzBLL2VCOXowZFlCamNEdlhKODE0M2QKV1NPN3ZnNWVBZCtOMkpPYjNDbEZhaFJHTmFCc01TaUdjNnNDdkovY0xxMjlqV0hPOWhwd25CYlFjWGh3azk2bwp3enUxWXk4ckIya3lYVWp1bWtqeFJ0blFmdklJSGF1Qlc3Z1RLaE41SnVGK2RVNWVseFRCRFZ3V2hUYjBaaXhDCi80UzBUK3Zta0gvR0l0amNyRW9mRGtFbWZIM0RZdklXT1pncWt0QVZIcVNYMTI0VGprTHJGR3BaYlJVNkNmY1IKVitNQS9DNjZNRG9MdWFMYUp1STBGOStGdHhDWkFDYjJyU2dGYjMyV2c3bm5hRVI5dWdKbnVQamU2ZUdGV3JtTwpsVVBIV3orS1VNRzhMNkNiTTk5ZTJMMS91eFhMQWdNQkFBR2pXVEJYTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQCkJnTlZIUk1CQWY4RUJUQURBUUgvTUIwR0ExVWREZ1FXQkJTYm5MWm0rczIxSExHY3VnS2hRekRqdkVWbWhUQVYKQmdOVkhSRUVEakFNZ2dwcmRXSmxjbTVsZEdWek1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRQUVqRWRhSjlyUwpkSitKcGtkMk1NZTF2SlVranRnSG16RGFkTlVCVEZEVWFta2hjNW1PaGs3NVNjM3Q2UElxbWlkeG9Od2FIV3pxCnhjS0JzRE82M3Nnb2tHSFhUbys4OERMTUt3UHlpSG1DaWhPVEdiUlhORmpRZmpwOXVhRFJWR3NBKzN3OFhEVXYKTlk2c0dBaGU0dTZ2WVNYRUQrWHJ0VlZ6Ujl6bGl1U0hiL3BtYms1cWVPNkd3N0psZDR3Y3N2My93ZzZyV1NwLwozMktqdnFkVmIzTVJ1WDRyemZsSnRMVUhFWEYwYWJaK1dGanBPZjl5MUF5U1g3Tk9jNm9tQXR0L2QyQnJuWktlCmwwNXRTTC9QMFlKTkdtZnBoT29rK0R2SjZneHdlL0UzUHhjeVN4Q1IrUU8yN2FPOS92eHkydkZyaGxUVEJET0MKWkN5MWdmbnpRU0lNCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K"}], "cluster_id": null, "compute_config": [], "created_at": "2025-06-04T10:23:59Z", "enabled_cluster_log_types": ["api", "audit", "authenticator"], "encryption_config": [{"provider": [{"key_arn": "arn:aws:kms:us-east-1:************:key/5d703b15-0d96-40e7-b3bb-31ec44cec062"}], "resources": ["secrets"]}], "endpoint": "https://BC79ED0BC3A47A644D565260098D7B0B.gr7.us-east-1.eks.amazonaws.com", "force_update_version": null, "id": "az-eks-cluster", "identity": [{"oidc": [{"issuer": "https://oidc.eks.us-east-1.amazonaws.com/id/BC79ED0BC3A47A644D565260098D7B0B"}]}], "kubernetes_network_config": [{"elastic_load_balancing": [{"enabled": false}], "ip_family": "ipv4", "service_ipv4_cidr": "**********/16", "service_ipv6_cidr": ""}], "name": "az-eks-cluster", "outpost_config": [], "platform_version": "eks.11", "remote_network_config": [], "role_arn": "arn:aws:iam::************:role/az-eks-cluster-cluster-20250604102327690200000001", "status": "ACTIVE", "storage_config": [], "tags": {"Environment": "dev", "Project": "eks-loki", "Terraform": "true"}, "tags_all": {"Environment": "dev", "Project": "eks-loki", "Terraform": "true"}, "timeouts": {"create": null, "delete": null, "update": null}, "upgrade_policy": [{"support_type": "EXTENDED"}], "version": "1.32", "vpc_config": [{"cluster_security_group_id": "sg-0d23dd552779a09ca", "endpoint_private_access": true, "endpoint_public_access": true, "public_access_cidrs": ["0.0.0.0/0"], "security_group_ids": ["sg-0d04fbdcf27b855f7"], "subnet_ids": ["subnet-08b224a511f12922c", "subnet-0d2d62e0364eb29ef"], "vpc_id": "vpc-0b14542ee3253a18b"}], "zonal_shift_config": []}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxODAwMDAwMDAwMDAwLCJkZWxldGUiOjkwMDAwMDAwMDAwMCwidXBkYXRlIjozNjAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["aws_iam_role.eks_cluster", "data.aws_availability_zones.available", "module.eks.aws_cloudwatch_log_group.this", "module.eks.aws_iam_policy.cni_ipv6_policy", "module.eks.aws_iam_role.this", "module.eks.aws_iam_role_policy_attachment.this", "module.eks.aws_security_group.cluster", "module.eks.aws_security_group.node", "module.eks.aws_security_group_rule.cluster", "module.eks.aws_security_group_rule.node", "module.eks.data.aws_caller_identity.current", "module.eks.data.aws_iam_policy_document.assume_role_policy", "module.eks.data.aws_iam_policy_document.cni_ipv6_policy", "module.eks.data.aws_iam_session_context.current", "module.eks.data.aws_partition.current", "module.eks.module.kms.aws_kms_external_key.this", "module.eks.module.kms.aws_kms_key.this", "module.eks.module.kms.aws_kms_replica_external_key.this", "module.eks.module.kms.aws_kms_replica_key.this", "module.eks.module.kms.data.aws_caller_identity.current", "module.eks.module.kms.data.aws_iam_policy_document.this", "module.eks.module.kms.data.aws_partition.current", "module.vpc.aws_subnet.private", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"], "create_before_destroy": true}]}, {"module": "module.eks", "mode": "managed", "type": "aws_eks_identity_provider_config", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.eks", "mode": "managed", "type": "aws_iam_openid_connect_provider", "name": "oidc_provider", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:oidc-provider/oidc.eks.us-east-1.amazonaws.com/id/BC79ED0BC3A47A644D565260098D7B0B", "client_id_list": ["sts.amazonaws.com"], "id": "arn:aws:iam::************:oidc-provider/oidc.eks.us-east-1.amazonaws.com/id/BC79ED0BC3A47A644D565260098D7B0B", "tags": {"Environment": "dev", "Name": "az-eks-cluster-eks-irsa", "Project": "eks-loki", "Terraform": "true"}, "tags_all": {"Environment": "dev", "Name": "az-eks-cluster-eks-irsa", "Project": "eks-loki", "Terraform": "true"}, "thumbprint_list": ["9e99a48a9960b14926bb7f3b02e22da2b0ab7280"], "url": "oidc.eks.us-east-1.amazonaws.com/id/BC79ED0BC3A47A644D565260098D7B0B"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks_cluster", "data.aws_availability_zones.available", "module.eks.aws_cloudwatch_log_group.this", "module.eks.aws_eks_cluster.this", "module.eks.aws_iam_policy.cni_ipv6_policy", "module.eks.aws_iam_role.this", "module.eks.aws_iam_role_policy_attachment.this", "module.eks.aws_security_group.cluster", "module.eks.aws_security_group.node", "module.eks.aws_security_group_rule.cluster", "module.eks.aws_security_group_rule.node", "module.eks.data.aws_caller_identity.current", "module.eks.data.aws_iam_policy_document.assume_role_policy", "module.eks.data.aws_iam_policy_document.cni_ipv6_policy", "module.eks.data.aws_iam_session_context.current", "module.eks.data.aws_partition.current", "module.eks.data.tls_certificate.this", "module.eks.module.kms.aws_kms_external_key.this", "module.eks.module.kms.aws_kms_key.this", "module.eks.module.kms.aws_kms_replica_external_key.this", "module.eks.module.kms.aws_kms_replica_key.this", "module.eks.module.kms.data.aws_caller_identity.current", "module.eks.module.kms.data.aws_iam_policy_document.this", "module.eks.module.kms.data.aws_partition.current", "module.vpc.aws_subnet.private", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"]}]}, {"module": "module.eks", "mode": "managed", "type": "aws_iam_policy", "name": "cluster_encryption", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:policy/az-eks-cluster-cluster-ClusterEncryption20250604102356789500000012", "attachment_count": 1, "description": "Cluster encryption policy to allow cluster role to utilize CMK provided", "id": "arn:aws:iam::************:policy/az-eks-cluster-cluster-ClusterEncryption20250604102356789500000012", "name": "az-eks-cluster-cluster-ClusterEncryption20250604102356789500000012", "name_prefix": "az-eks-cluster-cluster-ClusterEncryption", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"kms:Encrypt\",\"kms:Decrypt\",\"kms:ListGrants\",\"kms:DescribeKey\"],\"Effect\":\"Allow\",\"Resource\":\"arn:aws:kms:us-east-1:************:key/5d703b15-0d96-40e7-b3bb-31ec44cec062\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPATXF4JQPHVS3FKYJ6A", "tags": {"Environment": "dev", "Project": "eks-loki", "Terraform": "true"}, "tags_all": {"Environment": "dev", "Project": "eks-loki", "Terraform": "true"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks_cluster", "module.eks.aws_iam_role.this", "module.eks.data.aws_caller_identity.current", "module.eks.data.aws_iam_policy_document.assume_role_policy", "module.eks.data.aws_iam_session_context.current", "module.eks.data.aws_partition.current", "module.eks.module.kms.aws_kms_external_key.this", "module.eks.module.kms.aws_kms_key.this", "module.eks.module.kms.aws_kms_replica_external_key.this", "module.eks.module.kms.aws_kms_replica_key.this", "module.eks.module.kms.data.aws_caller_identity.current", "module.eks.module.kms.data.aws_iam_policy_document.this", "module.eks.module.kms.data.aws_partition.current"]}]}, {"module": "module.eks", "mode": "managed", "type": "aws_iam_policy", "name": "cni_ipv6_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.eks", "mode": "managed", "type": "aws_iam_role", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/az-eks-cluster-cluster-20250604102327690200000001", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"eks.amazonaws.com\"},\"Sid\":\"EKSClusterAssumeRole\"}],\"Version\":\"2012-10-17\"}", "create_date": "2025-06-04T10:23:28Z", "description": "", "force_detach_policies": true, "id": "az-eks-cluster-cluster-20250604102327690200000001", "inline_policy": [{"name": "az-eks-cluster-cluster", "policy": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Action\":[\"logs:CreateLogGroup\"],\"Effect\":\"Deny\",\"Resource\":\"*\"}]}"}], "managed_policy_arns": ["arn:aws:iam::************:policy/az-eks-cluster-cluster-ClusterEncryption20250604102356789500000012", "arn:aws:iam::aws:policy/AmazonEKSClusterPolicy", "arn:aws:iam::aws:policy/AmazonEKSVPCResourceController"], "max_session_duration": 3600, "name": "az-eks-cluster-cluster-20250604102327690200000001", "name_prefix": "az-eks-cluster-cluster-", "path": "/", "permissions_boundary": "", "tags": {"Environment": "dev", "Project": "eks-loki", "Terraform": "true"}, "tags_all": {"Environment": "dev", "Project": "eks-loki", "Terraform": "true"}, "unique_id": "AROATXF4JQPHTJTMLBFYS"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.eks.data.aws_iam_policy_document.assume_role_policy", "module.eks.data.aws_partition.current"], "create_before_destroy": true}]}, {"module": "module.eks", "mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "additional", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.eks", "mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "cluster_encryption", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"id": "az-eks-cluster-cluster-20250604102327690200000001-20250604102357932400000013", "policy_arn": "arn:aws:iam::************:policy/az-eks-cluster-cluster-ClusterEncryption20250604102356789500000012", "role": "az-eks-cluster-cluster-20250604102327690200000001"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks_cluster", "module.eks.aws_iam_policy.cluster_encryption", "module.eks.aws_iam_role.this", "module.eks.data.aws_caller_identity.current", "module.eks.data.aws_iam_policy_document.assume_role_policy", "module.eks.data.aws_iam_session_context.current", "module.eks.data.aws_partition.current", "module.eks.module.kms.aws_kms_external_key.this", "module.eks.module.kms.aws_kms_key.this", "module.eks.module.kms.aws_kms_replica_external_key.this", "module.eks.module.kms.aws_kms_replica_key.this", "module.eks.module.kms.data.aws_caller_identity.current", "module.eks.module.kms.data.aws_iam_policy_document.this", "module.eks.module.kms.data.aws_partition.current"]}]}, {"module": "module.eks", "mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "AmazonEKSClusterPolicy", "schema_version": 0, "attributes": {"id": "az-eks-cluster-cluster-20250604102327690200000001-20250604102330964500000009", "policy_arn": "arn:aws:iam::aws:policy/AmazonEKSClusterPolicy", "role": "az-eks-cluster-cluster-20250604102327690200000001"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.eks.aws_iam_role.this", "module.eks.data.aws_iam_policy_document.assume_role_policy", "module.eks.data.aws_partition.current"], "create_before_destroy": true}, {"index_key": "AmazonEKSVPCResourceController", "schema_version": 0, "attributes": {"id": "az-eks-cluster-cluster-20250604102327690200000001-20250604102330933500000008", "policy_arn": "arn:aws:iam::aws:policy/AmazonEKSVPCResourceController", "role": "az-eks-cluster-cluster-20250604102327690200000001"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.eks.aws_iam_role.this", "module.eks.data.aws_iam_policy_document.assume_role_policy", "module.eks.data.aws_partition.current"], "create_before_destroy": true}]}, {"module": "module.eks", "mode": "managed", "type": "aws_security_group", "name": "cluster", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:security-group/sg-0d04fbdcf27b855f7", "description": "EKS cluster security group", "egress": [], "id": "sg-0d04fbdcf27b855f7", "ingress": [{"cidr_blocks": [], "description": "Node groups to cluster API", "from_port": 443, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0a2ad203f65e425f6"], "self": false, "to_port": 443}], "name": "az-eks-cluster-cluster-2025060410234251230000000f", "name_prefix": "az-eks-cluster-cluster-", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Environment": "dev", "Name": "az-eks-cluster-cluster", "Project": "eks-loki", "Terraform": "true"}, "tags_all": {"Environment": "dev", "Name": "az-eks-cluster-cluster", "Project": "eks-loki", "Terraform": "true"}, "timeouts": null, "vpc_id": "vpc-0b14542ee3253a18b"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["module.vpc.aws_vpc.this"], "create_before_destroy": true}]}, {"module": "module.eks", "mode": "managed", "type": "aws_security_group", "name": "node", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:security-group/sg-0a2ad203f65e425f6", "description": "EKS node shared security group", "egress": [{"cidr_blocks": ["0.0.0.0/0"], "description": "Allow all egress", "from_port": 0, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "-1", "security_groups": [], "self": false, "to_port": 0}], "id": "sg-0a2ad203f65e425f6", "ingress": [{"cidr_blocks": [], "description": "Cluster API to node 4443/tcp webhook", "from_port": 4443, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0d04fbdcf27b855f7"], "self": false, "to_port": 4443}, {"cidr_blocks": [], "description": "Cluster API to node 6443/tcp webhook", "from_port": 6443, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0d04fbdcf27b855f7"], "self": false, "to_port": 6443}, {"cidr_blocks": [], "description": "Cluster API to node 8443/tcp webhook", "from_port": 8443, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0d04fbdcf27b855f7"], "self": false, "to_port": 8443}, {"cidr_blocks": [], "description": "Cluster API to node 9443/tcp webhook", "from_port": 9443, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0d04fbdcf27b855f7"], "self": false, "to_port": 9443}, {"cidr_blocks": [], "description": "Cluster API to node groups", "from_port": 443, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0d04fbdcf27b855f7"], "self": false, "to_port": 443}, {"cidr_blocks": [], "description": "Cluster API to node kubelets", "from_port": 10250, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": ["sg-0d04fbdcf27b855f7"], "self": false, "to_port": 10250}, {"cidr_blocks": [], "description": "Node to node CoreDNS UDP", "from_port": 53, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "udp", "security_groups": [], "self": true, "to_port": 53}, {"cidr_blocks": [], "description": "Node to node CoreDNS", "from_port": 53, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": true, "to_port": 53}, {"cidr_blocks": [], "description": "Node to node ingress on ephemeral ports", "from_port": 1025, "ipv6_cidr_blocks": [], "prefix_list_ids": [], "protocol": "tcp", "security_groups": [], "self": true, "to_port": 65535}], "name": "az-eks-cluster-node-2025060410234251180000000d", "name_prefix": "az-eks-cluster-node-", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Environment": "dev", "Name": "az-eks-cluster-node", "Project": "eks-loki", "Terraform": "true", "kubernetes.io/cluster/az-eks-cluster": "owned"}, "tags_all": {"Environment": "dev", "Name": "az-eks-cluster-node", "Project": "eks-loki", "Terraform": "true", "kubernetes.io/cluster/az-eks-cluster": "owned"}, "timeouts": null, "vpc_id": "vpc-0b14542ee3253a18b"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6OTAwMDAwMDAwMDAwfSwic2NoZW1hX3ZlcnNpb24iOiIxIn0=", "dependencies": ["module.vpc.aws_vpc.this"], "create_before_destroy": true}]}, {"module": "module.eks", "mode": "managed", "type": "aws_security_group_rule", "name": "cluster", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "ingress_nodes_443", "schema_version": 2, "attributes": {"cidr_blocks": null, "description": "Node groups to cluster API", "from_port": 443, "id": "sgrule-**********", "ipv6_cidr_blocks": null, "prefix_list_ids": null, "protocol": "tcp", "security_group_id": "sg-0d04fbdcf27b855f7", "security_group_rule_id": "sgr-0aa4839330c84e5aa", "self": false, "source_security_group_id": "sg-0a2ad203f65e425f6", "timeouts": null, "to_port": 443, "type": "ingress"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["module.eks.aws_security_group.cluster", "module.eks.aws_security_group.node", "module.vpc.aws_vpc.this"], "create_before_destroy": true}]}, {"module": "module.eks", "mode": "managed", "type": "aws_security_group_rule", "name": "node", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "egress_all", "schema_version": 2, "attributes": {"cidr_blocks": ["0.0.0.0/0"], "description": "Allow all egress", "from_port": 0, "id": "sgrule-**********", "ipv6_cidr_blocks": null, "prefix_list_ids": [], "protocol": "-1", "security_group_id": "sg-0a2ad203f65e425f6", "security_group_rule_id": "sgr-01cd1e25104d48c79", "self": false, "source_security_group_id": null, "timeouts": null, "to_port": 0, "type": "egress"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["module.eks.aws_security_group.cluster", "module.eks.aws_security_group.node", "module.vpc.aws_vpc.this"], "create_before_destroy": true}, {"index_key": "ingress_cluster_443", "schema_version": 2, "attributes": {"cidr_blocks": null, "description": "Cluster API to node groups", "from_port": 443, "id": "sgrule-**********", "ipv6_cidr_blocks": null, "prefix_list_ids": [], "protocol": "tcp", "security_group_id": "sg-0a2ad203f65e425f6", "security_group_rule_id": "sgr-04a80f65b1b1303fd", "self": false, "source_security_group_id": "sg-0d04fbdcf27b855f7", "timeouts": null, "to_port": 443, "type": "ingress"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["module.eks.aws_security_group.cluster", "module.eks.aws_security_group.node", "module.vpc.aws_vpc.this"], "create_before_destroy": true}, {"index_key": "ingress_cluster_4443_webhook", "schema_version": 2, "attributes": {"cidr_blocks": null, "description": "Cluster API to node 4443/tcp webhook", "from_port": 4443, "id": "sgrule-4215998249", "ipv6_cidr_blocks": null, "prefix_list_ids": [], "protocol": "tcp", "security_group_id": "sg-0a2ad203f65e425f6", "security_group_rule_id": "sgr-030245a661f03a008", "self": false, "source_security_group_id": "sg-0d04fbdcf27b855f7", "timeouts": null, "to_port": 4443, "type": "ingress"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["module.eks.aws_security_group.cluster", "module.eks.aws_security_group.node", "module.vpc.aws_vpc.this"], "create_before_destroy": true}, {"index_key": "ingress_cluster_6443_webhook", "schema_version": 2, "attributes": {"cidr_blocks": null, "description": "Cluster API to node 6443/tcp webhook", "from_port": 6443, "id": "sgrule-839475391", "ipv6_cidr_blocks": null, "prefix_list_ids": [], "protocol": "tcp", "security_group_id": "sg-0a2ad203f65e425f6", "security_group_rule_id": "sgr-08df24d17eafd8795", "self": false, "source_security_group_id": "sg-0d04fbdcf27b855f7", "timeouts": null, "to_port": 6443, "type": "ingress"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["module.eks.aws_security_group.cluster", "module.eks.aws_security_group.node", "module.vpc.aws_vpc.this"], "create_before_destroy": true}, {"index_key": "ingress_cluster_8443_webhook", "schema_version": 2, "attributes": {"cidr_blocks": null, "description": "Cluster API to node 8443/tcp webhook", "from_port": 8443, "id": "sgrule-559242398", "ipv6_cidr_blocks": null, "prefix_list_ids": [], "protocol": "tcp", "security_group_id": "sg-0a2ad203f65e425f6", "security_group_rule_id": "sgr-0e558547438d80d2e", "self": false, "source_security_group_id": "sg-0d04fbdcf27b855f7", "timeouts": null, "to_port": 8443, "type": "ingress"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["module.eks.aws_security_group.cluster", "module.eks.aws_security_group.node", "module.vpc.aws_vpc.this"], "create_before_destroy": true}, {"index_key": "ingress_cluster_9443_webhook", "schema_version": 2, "attributes": {"cidr_blocks": null, "description": "Cluster API to node 9443/tcp webhook", "from_port": 9443, "id": "sgrule-1173647189", "ipv6_cidr_blocks": null, "prefix_list_ids": [], "protocol": "tcp", "security_group_id": "sg-0a2ad203f65e425f6", "security_group_rule_id": "sgr-0143bc27befbef5db", "self": false, "source_security_group_id": "sg-0d04fbdcf27b855f7", "timeouts": null, "to_port": 9443, "type": "ingress"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["module.eks.aws_security_group.cluster", "module.eks.aws_security_group.node", "module.vpc.aws_vpc.this"], "create_before_destroy": true}, {"index_key": "ingress_cluster_kubelet", "schema_version": 2, "attributes": {"cidr_blocks": null, "description": "Cluster API to node kubelets", "from_port": 10250, "id": "sgrule-1707511898", "ipv6_cidr_blocks": null, "prefix_list_ids": [], "protocol": "tcp", "security_group_id": "sg-0a2ad203f65e425f6", "security_group_rule_id": "sgr-07a31fd7d81fd447f", "self": false, "source_security_group_id": "sg-0d04fbdcf27b855f7", "timeouts": null, "to_port": 10250, "type": "ingress"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["module.eks.aws_security_group.cluster", "module.eks.aws_security_group.node", "module.vpc.aws_vpc.this"], "create_before_destroy": true}, {"index_key": "ingress_nodes_ephemeral", "schema_version": 2, "attributes": {"cidr_blocks": null, "description": "Node to node ingress on ephemeral ports", "from_port": 1025, "id": "sgrule-8472026", "ipv6_cidr_blocks": null, "prefix_list_ids": [], "protocol": "tcp", "security_group_id": "sg-0a2ad203f65e425f6", "security_group_rule_id": "sgr-02d6b923528e8e450", "self": true, "source_security_group_id": null, "timeouts": null, "to_port": 65535, "type": "ingress"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["module.eks.aws_security_group.cluster", "module.eks.aws_security_group.node", "module.vpc.aws_vpc.this"], "create_before_destroy": true}, {"index_key": "ingress_self_coredns_tcp", "schema_version": 2, "attributes": {"cidr_blocks": null, "description": "Node to node CoreDNS", "from_port": 53, "id": "sgrule-135781858", "ipv6_cidr_blocks": null, "prefix_list_ids": [], "protocol": "tcp", "security_group_id": "sg-0a2ad203f65e425f6", "security_group_rule_id": "sgr-0687c1891ce3fc5bc", "self": true, "source_security_group_id": null, "timeouts": null, "to_port": 53, "type": "ingress"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["module.eks.aws_security_group.cluster", "module.eks.aws_security_group.node", "module.vpc.aws_vpc.this"], "create_before_destroy": true}, {"index_key": "ingress_self_coredns_udp", "schema_version": 2, "attributes": {"cidr_blocks": null, "description": "Node to node CoreDNS UDP", "from_port": 53, "id": "sgrule-2890432013", "ipv6_cidr_blocks": null, "prefix_list_ids": [], "protocol": "udp", "security_group_id": "sg-0a2ad203f65e425f6", "security_group_rule_id": "sgr-010d21a3ae27346b9", "self": true, "source_security_group_id": null, "timeouts": null, "to_port": 53, "type": "ingress"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDB9LCJzY2hlbWFfdmVyc2lvbiI6IjIifQ==", "dependencies": ["module.eks.aws_security_group.cluster", "module.eks.aws_security_group.node", "module.vpc.aws_vpc.this"], "create_before_destroy": true}]}, {"module": "module.eks", "mode": "managed", "type": "kubernetes_config_map", "name": "aws_auth", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": []}, {"module": "module.eks", "mode": "managed", "type": "kubernetes_config_map_v1_data", "name": "aws_auth", "provider": "provider[\"registry.terraform.io/hashicorp/kubernetes\"]", "instances": []}, {"module": "module.eks", "mode": "managed", "type": "time_sleep", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/time\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"create_duration": "30s", "destroy_duration": null, "id": "2025-06-04T10:33:34Z", "triggers": {"cluster_certificate_authority_data": "LS0tLS1CRUdJTiBDRVJUSUZJQ0FURS0tLS0tCk1JSURCVENDQWUyZ0F3SUJBZ0lJQWozT0o3Qm9IV0F3RFFZSktvWklodmNOQVFFTEJRQXdGVEVUTUJFR0ExVUUKQXhNS2EzVmlaWEp1WlhSbGN6QWVGdzB5TlRBMk1EUXhNREl6TkRSYUZ3MHpOVEEyTURJeE1ESTRORFJhTUJVeApFekFSQmdOVkJBTVRDbXQxWW1WeWJtVjBaWE13Z2dFaU1BMEdDU3FHU0liM0RRRUJBUVVBQTRJQkR3QXdnZ0VLCkFvSUJBUUNVOW5DcTczdHJXaGx3S3l4WG9HdjRHUk1raVk4R2c0ckMzUzBLL2VCOXowZFlCamNEdlhKODE0M2QKV1NPN3ZnNWVBZCtOMkpPYjNDbEZhaFJHTmFCc01TaUdjNnNDdkovY0xxMjlqV0hPOWhwd25CYlFjWGh3azk2bwp3enUxWXk4ckIya3lYVWp1bWtqeFJ0blFmdklJSGF1Qlc3Z1RLaE41SnVGK2RVNWVseFRCRFZ3V2hUYjBaaXhDCi80UzBUK3Zta0gvR0l0amNyRW9mRGtFbWZIM0RZdklXT1pncWt0QVZIcVNYMTI0VGprTHJGR3BaYlJVNkNmY1IKVitNQS9DNjZNRG9MdWFMYUp1STBGOStGdHhDWkFDYjJyU2dGYjMyV2c3bm5hRVI5dWdKbnVQamU2ZUdGV3JtTwpsVVBIV3orS1VNRzhMNkNiTTk5ZTJMMS91eFhMQWdNQkFBR2pXVEJYTUE0R0ExVWREd0VCL3dRRUF3SUNwREFQCkJnTlZIUk1CQWY4RUJUQURBUUgvTUIwR0ExVWREZ1FXQkJTYm5MWm0rczIxSExHY3VnS2hRekRqdkVWbWhUQVYKQmdOVkhSRUVEakFNZ2dwcmRXSmxjbTVsZEdWek1BMEdDU3FHU0liM0RRRUJDd1VBQTRJQkFRQUVqRWRhSjlyUwpkSitKcGtkMk1NZTF2SlVranRnSG16RGFkTlVCVEZEVWFta2hjNW1PaGs3NVNjM3Q2UElxbWlkeG9Od2FIV3pxCnhjS0JzRE82M3Nnb2tHSFhUbys4OERMTUt3UHlpSG1DaWhPVEdiUlhORmpRZmpwOXVhRFJWR3NBKzN3OFhEVXYKTlk2c0dBaGU0dTZ2WVNYRUQrWHJ0VlZ6Ujl6bGl1U0hiL3BtYms1cWVPNkd3N0psZDR3Y3N2My93ZzZyV1NwLwozMktqdnFkVmIzTVJ1WDRyemZsSnRMVUhFWEYwYWJaK1dGanBPZjl5MUF5U1g3Tk9jNm9tQXR0L2QyQnJuWktlCmwwNXRTTC9QMFlKTkdtZnBoT29rK0R2SjZneHdlL0UzUHhjeVN4Q1IrUU8yN2FPOS92eHkydkZyaGxUVEJET0MKWkN5MWdmbnpRU0lNCi0tLS0tRU5EIENFUlRJRklDQVRFLS0tLS0K", "cluster_endpoint": "https://BC79ED0BC3A47A644D565260098D7B0B.gr7.us-east-1.eks.amazonaws.com", "cluster_name": "az-eks-cluster", "cluster_version": "1.32"}}, "sensitive_attributes": [], "identity_schema_version": 0, "dependencies": ["aws_iam_role.eks_cluster", "data.aws_availability_zones.available", "module.eks.aws_cloudwatch_log_group.this", "module.eks.aws_eks_cluster.this", "module.eks.aws_iam_policy.cni_ipv6_policy", "module.eks.aws_iam_role.this", "module.eks.aws_iam_role_policy_attachment.this", "module.eks.aws_security_group.cluster", "module.eks.aws_security_group.node", "module.eks.aws_security_group_rule.cluster", "module.eks.aws_security_group_rule.node", "module.eks.data.aws_caller_identity.current", "module.eks.data.aws_iam_policy_document.assume_role_policy", "module.eks.data.aws_iam_policy_document.cni_ipv6_policy", "module.eks.data.aws_iam_session_context.current", "module.eks.data.aws_partition.current", "module.eks.module.kms.aws_kms_external_key.this", "module.eks.module.kms.aws_kms_key.this", "module.eks.module.kms.aws_kms_replica_external_key.this", "module.eks.module.kms.aws_kms_replica_key.this", "module.eks.module.kms.data.aws_caller_identity.current", "module.eks.module.kms.data.aws_iam_policy_document.this", "module.eks.module.kms.data.aws_partition.current", "module.vpc.aws_subnet.private", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"], "create_before_destroy": true}]}, {"module": "module.eks.module.eks_managed_node_group[\"general\"]", "mode": "data", "type": "aws_caller_identity", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "************", "arn": "arn:aws:iam::************:user/azni_ce9", "id": "************", "user_id": "AIDATXF4JQPHQNHQA2SKB"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.eks.module.eks_managed_node_group[\"general\"]", "mode": "data", "type": "aws_iam_policy_document", "name": "assume_role_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Sid\": \"EKSNodeAssumeRole\",\n      \"Effect\": \"Allow\",\n      \"Action\": \"sts:AssumeRole\",\n      \"Principal\": {\n        \"Service\": \"ec2.amazonaws.com\"\n      }\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Sid\":\"EKSNodeAssumeRole\",\"Effect\":\"Allow\",\"Action\":\"sts:AssumeRole\",\"Principal\":{\"Service\":\"ec2.amazonaws.com\"}}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["sts:<PERSON><PERSON>Role"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["ec2.amazonaws.com"], "type": "Service"}], "resources": [], "sid": "EKSNodeAssumeRole"}], "version": "2012-10-17"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.eks.module.eks_managed_node_group[\"general\"]", "mode": "data", "type": "aws_partition", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"dns_suffix": "amazonaws.com", "id": "aws", "partition": "aws", "reverse_dns_prefix": "com.amazonaws"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.eks.module.eks_managed_node_group[\"general\"]", "mode": "managed", "type": "aws_autoscaling_schedule", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.eks.module.eks_managed_node_group[\"general\"]", "mode": "managed", "type": "aws_eks_node_group", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"ami_type": "AL2023_x86_64_STANDARD", "arn": "arn:aws:eks:us-east-1:************:nodegroup/az-eks-cluster/general-20250604103341508600000016/6ecb9d40-7f2c-71fc-cd60-701a14cc89d7", "capacity_type": "ON_DEMAND", "cluster_name": "az-eks-cluster", "disk_size": 0, "force_update_version": null, "id": "az-eks-cluster:general-20250604103341508600000016", "instance_types": ["t3.medium"], "labels": {"Environment": "dev"}, "launch_template": [{"id": "lt-0322b03662b970784", "name": "general-20250604103334281600000014", "version": "1"}], "node_group_name": "general-20250604103341508600000016", "node_group_name_prefix": "general-", "node_repair_config": [], "node_role_arn": "arn:aws:iam::************:role/general-eks-node-group-20250604102329691600000002", "release_version": "1.32.3-20250519", "remote_access": [], "resources": [{"autoscaling_groups": [{"name": "eks-general-20250604103341508600000016-6ecb9d40-7f2c-71fc-cd60-701a14cc89d7"}], "remote_access_security_group_id": ""}], "scaling_config": [{"desired_size": 2, "max_size": 3, "min_size": 2}], "status": "ACTIVE", "subnet_ids": ["subnet-08b224a511f12922c", "subnet-0d2d62e0364eb29ef"], "tags": {"Environment": "dev", "Name": "general", "Project": "eks-loki", "Terraform": "true"}, "tags_all": {"Environment": "dev", "Name": "general", "Project": "eks-loki", "Terraform": "true"}, "taint": [], "timeouts": {"create": null, "delete": null, "update": null}, "update_config": [{"max_unavailable": 0, "max_unavailable_percentage": 33}], "version": "1.32"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozNjAwMDAwMDAwMDAwLCJkZWxldGUiOjM2MDAwMDAwMDAwMDAsInVwZGF0ZSI6MzYwMDAwMDAwMDAwMH19", "dependencies": ["aws_iam_role.eks_cluster", "aws_iam_role.eks_node_group", "data.aws_availability_zones.available", "module.eks.aws_cloudwatch_log_group.this", "module.eks.aws_eks_cluster.this", "module.eks.aws_iam_policy.cni_ipv6_policy", "module.eks.aws_iam_role.this", "module.eks.aws_iam_role_policy_attachment.this", "module.eks.aws_security_group.cluster", "module.eks.aws_security_group.node", "module.eks.aws_security_group_rule.cluster", "module.eks.aws_security_group_rule.node", "module.eks.data.aws_caller_identity.current", "module.eks.data.aws_iam_policy_document.assume_role_policy", "module.eks.data.aws_iam_policy_document.cni_ipv6_policy", "module.eks.data.aws_iam_session_context.current", "module.eks.data.aws_partition.current", "module.eks.module.eks_managed_node_group.aws_iam_role.this", "module.eks.module.eks_managed_node_group.aws_iam_role_policy_attachment.this", "module.eks.module.eks_managed_node_group.aws_launch_template.this", "module.eks.module.eks_managed_node_group.data.aws_caller_identity.current", "module.eks.module.eks_managed_node_group.data.aws_iam_policy_document.assume_role_policy", "module.eks.module.eks_managed_node_group.data.aws_partition.current", "module.eks.module.eks_managed_node_group.module.user_data.data.cloudinit_config.linux_eks_managed_node_group", "module.eks.module.kms.aws_kms_external_key.this", "module.eks.module.kms.aws_kms_key.this", "module.eks.module.kms.aws_kms_replica_external_key.this", "module.eks.module.kms.aws_kms_replica_key.this", "module.eks.module.kms.data.aws_caller_identity.current", "module.eks.module.kms.data.aws_iam_policy_document.this", "module.eks.module.kms.data.aws_partition.current", "module.eks.time_sleep.this", "module.vpc.aws_subnet.private", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"], "create_before_destroy": true}]}, {"module": "module.eks.module.eks_managed_node_group[\"general\"]", "mode": "managed", "type": "aws_iam_role", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/general-eks-node-group-20250604102329691600000002", "assume_role_policy": "{\"Statement\":[{\"Action\":\"sts:AssumeRole\",\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"ec2.amazonaws.com\"},\"Sid\":\"EKSNodeAssumeRole\"}],\"Version\":\"2012-10-17\"}", "create_date": "2025-06-04T10:23:30Z", "description": "EKS managed node group IAM role", "force_detach_policies": true, "id": "general-eks-node-group-20250604102329691600000002", "inline_policy": [], "managed_policy_arns": ["arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly", "arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy", "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy"], "max_session_duration": 3600, "name": "general-eks-node-group-20250604102329691600000002", "name_prefix": "general-eks-node-group-", "path": "/", "permissions_boundary": "", "tags": {"Environment": "dev", "Project": "eks-loki", "Terraform": "true"}, "tags_all": {"Environment": "dev", "Project": "eks-loki", "Terraform": "true"}, "unique_id": "AROATXF4JQPHUSVNAAPND"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks_node_group", "module.eks.module.eks_managed_node_group.data.aws_iam_policy_document.assume_role_policy", "module.eks.module.eks_managed_node_group.data.aws_partition.current"], "create_before_destroy": true}]}, {"module": "module.eks.module.eks_managed_node_group[\"general\"]", "mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "additional", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.eks.module.eks_managed_node_group[\"general\"]", "mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly", "schema_version": 0, "attributes": {"id": "general-eks-node-group-20250604102329691600000002-2025060410233168380000000a", "policy_arn": "arn:aws:iam::aws:policy/AmazonEC2ContainerRegistryReadOnly", "role": "general-eks-node-group-20250604102329691600000002"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks_node_group", "module.eks.module.eks_managed_node_group.aws_iam_role.this", "module.eks.module.eks_managed_node_group.data.aws_caller_identity.current", "module.eks.module.eks_managed_node_group.data.aws_iam_policy_document.assume_role_policy", "module.eks.module.eks_managed_node_group.data.aws_partition.current"], "create_before_destroy": true}, {"index_key": "arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy", "schema_version": 0, "attributes": {"id": "general-eks-node-group-20250604102329691600000002-2025060410233170640000000c", "policy_arn": "arn:aws:iam::aws:policy/AmazonEKSWorkerNodePolicy", "role": "general-eks-node-group-20250604102329691600000002"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks_node_group", "module.eks.module.eks_managed_node_group.aws_iam_role.this", "module.eks.module.eks_managed_node_group.data.aws_caller_identity.current", "module.eks.module.eks_managed_node_group.data.aws_iam_policy_document.assume_role_policy", "module.eks.module.eks_managed_node_group.data.aws_partition.current"], "create_before_destroy": true}, {"index_key": "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy", "schema_version": 0, "attributes": {"id": "general-eks-node-group-20250604102329691600000002-2025060410233168530000000b", "policy_arn": "arn:aws:iam::aws:policy/AmazonEKS_CNI_Policy", "role": "general-eks-node-group-20250604102329691600000002"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks_node_group", "module.eks.module.eks_managed_node_group.aws_iam_role.this", "module.eks.module.eks_managed_node_group.data.aws_caller_identity.current", "module.eks.module.eks_managed_node_group.data.aws_iam_policy_document.assume_role_policy", "module.eks.module.eks_managed_node_group.data.aws_partition.current"], "create_before_destroy": true}]}, {"module": "module.eks.module.eks_managed_node_group[\"general\"]", "mode": "managed", "type": "aws_launch_template", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:launch-template/lt-0322b03662b970784", "block_device_mappings": [], "capacity_reservation_specification": [], "cpu_options": [], "credit_specification": [], "default_version": 1, "description": "Custom launch template for general EKS managed node group", "disable_api_stop": false, "disable_api_termination": false, "ebs_optimized": "", "elastic_gpu_specifications": [], "elastic_inference_accelerator": [], "enclave_options": [], "hibernation_options": [], "iam_instance_profile": [], "id": "lt-0322b03662b970784", "image_id": "", "instance_initiated_shutdown_behavior": "", "instance_market_options": [], "instance_requirements": [], "instance_type": "", "kernel_id": "", "key_name": "", "latest_version": 1, "license_specification": [], "maintenance_options": [], "metadata_options": [{"http_endpoint": "enabled", "http_protocol_ipv6": "", "http_put_response_hop_limit": 2, "http_tokens": "required", "instance_metadata_tags": ""}], "monitoring": [{"enabled": true}], "name": "general-20250604103334281600000014", "name_prefix": "general-", "network_interfaces": [], "placement": [], "private_dns_name_options": [], "ram_disk_id": "", "security_group_names": [], "tag_specifications": [{"resource_type": "instance", "tags": {"Environment": "dev", "Name": "general", "Project": "eks-loki", "Terraform": "true"}}, {"resource_type": "network-interface", "tags": {"Environment": "dev", "Name": "general", "Project": "eks-loki", "Terraform": "true"}}, {"resource_type": "volume", "tags": {"Environment": "dev", "Name": "general", "Project": "eks-loki", "Terraform": "true"}}], "tags": {"Environment": "dev", "Project": "eks-loki", "Terraform": "true"}, "tags_all": {"Environment": "dev", "Project": "eks-loki", "Terraform": "true"}, "update_default_version": true, "user_data": "", "vpc_security_group_ids": ["sg-0a2ad203f65e425f6"]}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks_cluster", "aws_iam_role.eks_node_group", "data.aws_availability_zones.available", "module.eks.aws_cloudwatch_log_group.this", "module.eks.aws_eks_cluster.this", "module.eks.aws_iam_policy.cni_ipv6_policy", "module.eks.aws_iam_role.this", "module.eks.aws_iam_role_policy_attachment.this", "module.eks.aws_security_group.cluster", "module.eks.aws_security_group.node", "module.eks.aws_security_group_rule.cluster", "module.eks.aws_security_group_rule.node", "module.eks.data.aws_caller_identity.current", "module.eks.data.aws_iam_policy_document.assume_role_policy", "module.eks.data.aws_iam_policy_document.cni_ipv6_policy", "module.eks.data.aws_iam_session_context.current", "module.eks.data.aws_partition.current", "module.eks.module.eks_managed_node_group.aws_iam_role.this", "module.eks.module.eks_managed_node_group.aws_iam_role_policy_attachment.this", "module.eks.module.eks_managed_node_group.data.aws_caller_identity.current", "module.eks.module.eks_managed_node_group.data.aws_iam_policy_document.assume_role_policy", "module.eks.module.eks_managed_node_group.data.aws_partition.current", "module.eks.module.eks_managed_node_group.module.user_data.data.cloudinit_config.linux_eks_managed_node_group", "module.eks.module.kms.aws_kms_external_key.this", "module.eks.module.kms.aws_kms_key.this", "module.eks.module.kms.aws_kms_replica_external_key.this", "module.eks.module.kms.aws_kms_replica_key.this", "module.eks.module.kms.data.aws_caller_identity.current", "module.eks.module.kms.data.aws_iam_policy_document.this", "module.eks.module.kms.data.aws_partition.current", "module.eks.time_sleep.this", "module.vpc.aws_subnet.private", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"], "create_before_destroy": true}]}, {"module": "module.eks.module.eks_managed_node_group[\"general\"].module.user_data", "mode": "data", "type": "cloudinit_config", "name": "linux_eks_managed_node_group", "provider": "provider[\"registry.terraform.io/hashicorp/cloudinit\"]", "instances": []}, {"module": "module.eks.module.kms", "mode": "data", "type": "aws_caller_identity", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"account_id": "************", "arn": "arn:aws:iam::************:user/azni_ce9", "id": "************", "user_id": "AIDATXF4JQPHQNHQA2SKB"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.eks.module.kms", "mode": "data", "type": "aws_iam_policy_document", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Sid\": \"KeyAdministration\",\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"kms:Update*\",\n        \"kms:UntagResource\",\n        \"kms:TagResource\",\n        \"kms:ScheduleKeyDeletion\",\n        \"kms:Revoke*\",\n        \"kms:ReplicateKey\",\n        \"kms:Put*\",\n        \"kms:List*\",\n        \"kms:ImportKeyMaterial\",\n        \"kms:Get*\",\n        \"kms:Enable*\",\n        \"kms:Disable*\",\n        \"kms:Describe*\",\n        \"kms:Delete*\",\n        \"kms:Create*\",\n        \"kms:CancelKeyDeletion\"\n      ],\n      \"Resource\": \"*\",\n      \"Principal\": {\n        \"AWS\": \"arn:aws:iam::************:user/azni_ce9\"\n      }\n    },\n    {\n      \"Sid\": \"KeyUsage\",\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"kms:ReEncrypt*\",\n        \"kms:GenerateDataKey*\",\n        \"kms:Encrypt\",\n        \"kms:DescribeKey\",\n        \"kms:Decrypt\"\n      ],\n      \"Resource\": \"*\",\n      \"Principal\": {\n        \"AWS\": \"arn:aws:iam::************:role/az-eks-cluster-cluster-20250604102327690200000001\"\n      }\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Sid\":\"KeyAdministration\",\"Effect\":\"Allow\",\"Action\":[\"kms:Update*\",\"kms:UntagResource\",\"kms:TagResource\",\"kms:ScheduleKeyDeletion\",\"kms:Revoke*\",\"kms:ReplicateKey\",\"kms:Put*\",\"kms:List*\",\"kms:ImportKeyMaterial\",\"kms:Get*\",\"kms:Enable*\",\"kms:Disable*\",\"kms:Describe*\",\"kms:Delete*\",\"kms:Create*\",\"kms:CancelKeyDeletion\"],\"Resource\":\"*\",\"Principal\":{\"AWS\":\"arn:aws:iam::************:user/azni_ce9\"}},{\"Sid\":\"KeyUsage\",\"Effect\":\"Allow\",\"Action\":[\"kms:ReEncrypt*\",\"kms:GenerateDataKey*\",\"kms:Encrypt\",\"kms:DescribeKey\",\"kms:Decrypt\"],\"Resource\":\"*\",\"Principal\":{\"AWS\":\"arn:aws:iam::************:role/az-eks-cluster-cluster-20250604102327690200000001\"}}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["kms:CancelKeyDeletion", "kms:Create*", "kms:Delete*", "kms:Describe*", "kms:Disable*", "kms:Enable*", "kms:Get*", "kms:ImportKeyMaterial", "kms:List*", "kms:Put*", "kms:ReplicateKey", "kms:Revoke*", "kms:ScheduleKeyDeletion", "kms:TagResource", "kms:UntagResource", "kms:Update*"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["arn:aws:iam::************:user/azni_ce9"], "type": "AWS"}], "resources": ["*"], "sid": "KeyAdministration"}, {"actions": ["kms:Decrypt", "kms:DescribeKey", "kms:Encrypt", "kms:GenerateDataKey*", "kms:ReEncrypt*"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["arn:aws:iam::************:role/az-eks-cluster-cluster-20250604102327690200000001"], "type": "AWS"}], "resources": ["*"], "sid": "KeyUsage"}], "version": "2012-10-17"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.eks.module.kms", "mode": "data", "type": "aws_partition", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"dns_suffix": "amazonaws.com", "id": "aws", "partition": "aws", "reverse_dns_prefix": "com.amazonaws"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.eks.module.kms", "mode": "managed", "type": "aws_kms_alias", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "cluster", "schema_version": 0, "attributes": {"arn": "arn:aws:kms:us-east-1:************:alias/eks/az-eks-cluster", "id": "alias/eks/az-eks-cluster", "name": "alias/eks/az-eks-cluster", "name_prefix": "", "target_key_arn": "arn:aws:kms:us-east-1:************:key/5d703b15-0d96-40e7-b3bb-31ec44cec062", "target_key_id": "5d703b15-0d96-40e7-b3bb-31ec44cec062"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["aws_iam_role.eks_cluster", "module.eks.aws_iam_role.this", "module.eks.data.aws_caller_identity.current", "module.eks.data.aws_iam_policy_document.assume_role_policy", "module.eks.data.aws_iam_session_context.current", "module.eks.data.aws_partition.current", "module.eks.module.kms.aws_kms_external_key.this", "module.eks.module.kms.aws_kms_key.this", "module.eks.module.kms.aws_kms_replica_external_key.this", "module.eks.module.kms.aws_kms_replica_key.this", "module.eks.module.kms.data.aws_caller_identity.current", "module.eks.module.kms.data.aws_iam_policy_document.this", "module.eks.module.kms.data.aws_partition.current"]}]}, {"module": "module.eks.module.kms", "mode": "managed", "type": "aws_kms_external_key", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.eks.module.kms", "mode": "managed", "type": "aws_kms_grant", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.eks.module.kms", "mode": "managed", "type": "aws_kms_key", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:kms:us-east-1:************:key/5d703b15-0d96-40e7-b3bb-31ec44cec062", "bypass_policy_lockout_safety_check": false, "custom_key_store_id": "", "customer_master_key_spec": "SYMMETRIC_DEFAULT", "deletion_window_in_days": null, "description": "az-eks-cluster cluster encryption key", "enable_key_rotation": true, "id": "5d703b15-0d96-40e7-b3bb-31ec44cec062", "is_enabled": true, "key_id": "5d703b15-0d96-40e7-b3bb-31ec44cec062", "key_usage": "ENCRYPT_DECRYPT", "multi_region": false, "policy": "{\"Statement\":[{\"Action\":[\"kms:Update*\",\"kms:UntagResource\",\"kms:TagResource\",\"kms:ScheduleKeyDeletion\",\"kms:Revoke*\",\"kms:ReplicateKey\",\"kms:Put*\",\"kms:List*\",\"kms:ImportKeyMaterial\",\"kms:Get*\",\"kms:Enable*\",\"kms:Disable*\",\"kms:Describe*\",\"kms:Delete*\",\"kms:Create*\",\"kms:CancelKeyDeletion\"],\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"arn:aws:iam::************:user/azni_ce9\"},\"Resource\":\"*\",\"Sid\":\"KeyAdministration\"},{\"Action\":[\"kms:ReEncrypt*\",\"kms:GenerateDataKey*\",\"kms:Encrypt\",\"kms:DescribeKey\",\"kms:Decrypt\"],\"Effect\":\"Allow\",\"Principal\":{\"AWS\":\"arn:aws:iam::************:role/az-eks-cluster-cluster-20250604102327690200000001\"},\"Resource\":\"*\",\"Sid\":\"KeyUsage\"}],\"Version\":\"2012-10-17\"}", "rotation_period_in_days": 365, "tags": {"Environment": "dev", "Project": "eks-loki", "Terraform": "true"}, "tags_all": {"Environment": "dev", "Project": "eks-loki", "Terraform": "true"}, "timeouts": null, "xks_key_id": ""}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDB9fQ==", "dependencies": ["aws_iam_role.eks_cluster", "module.eks.aws_iam_role.this", "module.eks.data.aws_caller_identity.current", "module.eks.data.aws_iam_policy_document.assume_role_policy", "module.eks.data.aws_iam_session_context.current", "module.eks.data.aws_partition.current", "module.eks.module.kms.data.aws_caller_identity.current", "module.eks.module.kms.data.aws_iam_policy_document.this", "module.eks.module.kms.data.aws_partition.current"], "create_before_destroy": true}]}, {"module": "module.eks.module.kms", "mode": "managed", "type": "aws_kms_replica_external_key", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.eks.module.kms", "mode": "managed", "type": "aws_kms_replica_key", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "data", "type": "aws_iam_policy_document", "name": "flow_log_cloudwatch_assume_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "data", "type": "aws_iam_policy_document", "name": "vpc_flow_log_cloudwatch", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_cloudwatch_log_group", "name": "flow_log", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_customer_gateway", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_db_subnet_group", "name": "database", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_default_network_acl", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:network-acl/acl-0d21469bc2e71612d", "default_network_acl_id": "acl-0d21469bc2e71612d", "egress": [{"action": "allow", "cidr_block": "", "from_port": 0, "icmp_code": 0, "icmp_type": 0, "ipv6_cidr_block": "::/0", "protocol": "-1", "rule_no": 101, "to_port": 0}, {"action": "allow", "cidr_block": "0.0.0.0/0", "from_port": 0, "icmp_code": 0, "icmp_type": 0, "ipv6_cidr_block": "", "protocol": "-1", "rule_no": 100, "to_port": 0}], "id": "acl-0d21469bc2e71612d", "ingress": [{"action": "allow", "cidr_block": "", "from_port": 0, "icmp_code": 0, "icmp_type": 0, "ipv6_cidr_block": "::/0", "protocol": "-1", "rule_no": 101, "to_port": 0}, {"action": "allow", "cidr_block": "0.0.0.0/0", "from_port": 0, "icmp_code": 0, "icmp_type": 0, "ipv6_cidr_block": "", "protocol": "-1", "rule_no": 100, "to_port": 0}], "owner_id": "************", "subnet_ids": ["subnet-08b224a511f12922c", "subnet-0aa66c027fb4d1cb8", "subnet-0c0e13d2a1c174483", "subnet-0d2d62e0364eb29ef"], "tags": {"Environment": "dev", "Name": "az-eks-cluster-vpc-default", "Project": "eks-loki", "Terraform": "true"}, "tags_all": {"Environment": "dev", "Name": "az-eks-cluster-vpc-default", "Project": "eks-loki", "Terraform": "true"}, "vpc_id": "vpc-0b14542ee3253a18b"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.vpc.aws_vpc.this"]}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_default_route_table", "name": "default", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:route-table/rtb-0fa875a9301506986", "default_route_table_id": "rtb-0fa875a9301506986", "id": "rtb-0fa875a9301506986", "owner_id": "************", "propagating_vgws": [], "route": [], "tags": {"Environment": "dev", "Name": "az-eks-cluster-vpc-default", "Project": "eks-loki", "Terraform": "true"}, "tags_all": {"Environment": "dev", "Name": "az-eks-cluster-vpc-default", "Project": "eks-loki", "Terraform": "true"}, "timeouts": {"create": "5m", "update": "5m"}, "vpc_id": "vpc-0b14542ee3253a18b"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsInVwZGF0ZSI6MzAwMDAwMDAwMDAwfX0=", "dependencies": ["module.vpc.aws_vpc.this"]}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_default_security_group", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:security-group/sg-06fc7e07b18761f63", "description": "default VPC security group", "egress": [], "id": "sg-06fc7e07b18761f63", "ingress": [], "name": "default", "name_prefix": "", "owner_id": "************", "revoke_rules_on_delete": false, "tags": {"Environment": "dev", "Name": "az-eks-cluster-vpc-default", "Project": "eks-loki", "Terraform": "true"}, "tags_all": {"Environment": "dev", "Name": "az-eks-cluster-vpc-default", "Project": "eks-loki", "Terraform": "true"}, "vpc_id": "vpc-0b14542ee3253a18b"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "dependencies": ["module.vpc.aws_vpc.this"]}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_default_vpc", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_egress_only_internet_gateway", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_eip", "name": "nat", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"address": null, "allocation_id": "eipalloc-067c8139c6ed6bee4", "arn": "arn:aws:ec2:us-east-1:************:elastic-ip/eipalloc-067c8139c6ed6bee4", "associate_with_private_ip": null, "association_id": "eipassoc-03cd2743629bc0a55", "carrier_ip": "", "customer_owned_ip": "", "customer_owned_ipv4_pool": "", "domain": "vpc", "id": "eipalloc-067c8139c6ed6bee4", "instance": "", "ipam_pool_id": null, "network_border_group": "us-east-1", "network_interface": "eni-003cc1cc3ac2ad27c", "private_dns": "ip-10-0-40-40.ec2.internal", "private_ip": "**********", "ptr_record": "", "public_dns": "ec2-44-212-196-101.compute-1.amazonaws.com", "public_ip": "**************", "public_ipv4_pool": "amazon", "tags": {"Environment": "dev", "Name": "az-eks-cluster-vpc-us-east-1a", "Project": "eks-loki", "Terraform": "true"}, "tags_all": {"Environment": "dev", "Name": "az-eks-cluster-vpc-us-east-1a", "Project": "eks-loki", "Terraform": "true"}, "timeouts": null, "vpc": true}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiZGVsZXRlIjoxODAwMDAwMDAwMDAsInJlYWQiOjkwMDAwMDAwMDAwMCwidXBkYXRlIjozMDAwMDAwMDAwMDB9fQ==", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_internet_gateway.this", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"]}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_elasticache_subnet_group", "name": "elasticache", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_flow_log", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_iam_policy", "name": "vpc_flow_log_cloudwatch", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_iam_role", "name": "vpc_flow_log_cloudwatch", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "vpc_flow_log_cloudwatch", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_internet_gateway", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:internet-gateway/igw-0805fa453303fb8b6", "id": "igw-0805fa453303fb8b6", "owner_id": "************", "tags": {"Environment": "dev", "Name": "az-eks-cluster-vpc", "Project": "eks-loki", "Terraform": "true"}, "tags_all": {"Environment": "dev", "Name": "az-eks-cluster-vpc", "Project": "eks-loki", "Terraform": "true"}, "timeouts": null, "vpc_id": "vpc-0b14542ee3253a18b"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDAsInVwZGF0ZSI6MTIwMDAwMDAwMDAwMH19", "dependencies": ["module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"]}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_nat_gateway", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"allocation_id": "eipalloc-067c8139c6ed6bee4", "association_id": "eipassoc-03cd2743629bc0a55", "connectivity_type": "public", "id": "nat-0e8c6f035d7e4f59d", "network_interface_id": "eni-003cc1cc3ac2ad27c", "private_ip": "**********", "public_ip": "**************", "secondary_allocation_ids": [], "secondary_private_ip_address_count": 0, "secondary_private_ip_addresses": [], "subnet_id": "subnet-0c0e13d2a1c174483", "tags": {"Environment": "dev", "Name": "az-eks-cluster-vpc-us-east-1a", "Project": "eks-loki", "Terraform": "true"}, "tags_all": {"Environment": "dev", "Name": "az-eks-cluster-vpc-us-east-1a", "Project": "eks-loki", "Terraform": "true"}, "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTgwMDAwMDAwMDAwMCwidXBkYXRlIjo2MDAwMDAwMDAwMDB9fQ==", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_eip.nat", "module.vpc.aws_internet_gateway.this", "module.vpc.aws_subnet.public", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"]}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_network_acl", "name": "database", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_network_acl", "name": "elasticache", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_network_acl", "name": "intra", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_network_acl", "name": "outpost", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_network_acl", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_network_acl", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_network_acl", "name": "redshift", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_network_acl_rule", "name": "database_inbound", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_network_acl_rule", "name": "database_outbound", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_network_acl_rule", "name": "elasticache_inbound", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_network_acl_rule", "name": "elasticache_outbound", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_network_acl_rule", "name": "intra_inbound", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_network_acl_rule", "name": "intra_outbound", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_network_acl_rule", "name": "outpost_inbound", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_network_acl_rule", "name": "outpost_outbound", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_network_acl_rule", "name": "private_inbound", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_network_acl_rule", "name": "private_outbound", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_network_acl_rule", "name": "public_inbound", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_network_acl_rule", "name": "public_outbound", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_network_acl_rule", "name": "redshift_inbound", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_network_acl_rule", "name": "redshift_outbound", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_redshift_subnet_group", "name": "redshift", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_route", "name": "database_dns64_nat_gateway", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_route", "name": "database_internet_gateway", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_route", "name": "database_ipv6_egress", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_route", "name": "database_nat_gateway", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_route", "name": "private_dns64_nat_gateway", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_route", "name": "private_ipv6_egress", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_route", "name": "private_nat_gateway", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"carrier_gateway_id": "", "core_network_arn": "", "destination_cidr_block": "0.0.0.0/0", "destination_ipv6_cidr_block": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "id": "r-rtb-05dac9ab61d8c626e1080289494", "instance_id": "", "instance_owner_id": "", "local_gateway_id": "", "nat_gateway_id": "nat-0e8c6f035d7e4f59d", "network_interface_id": "", "origin": "CreateRoute", "route_table_id": "rtb-05dac9ab61d8c626e", "state": "active", "timeouts": {"create": "5m", "delete": null, "update": null}, "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_eip.nat", "module.vpc.aws_internet_gateway.this", "module.vpc.aws_nat_gateway.this", "module.vpc.aws_route_table.private", "module.vpc.aws_subnet.public", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"]}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_route", "name": "public_internet_gateway", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"carrier_gateway_id": "", "core_network_arn": "", "destination_cidr_block": "0.0.0.0/0", "destination_ipv6_cidr_block": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "igw-0805fa453303fb8b6", "id": "r-rtb-0b7394f1a99fdc2741080289494", "instance_id": "", "instance_owner_id": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "origin": "CreateRoute", "route_table_id": "rtb-0b7394f1a99fdc274", "state": "active", "timeouts": {"create": "5m", "delete": null, "update": null}, "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["module.vpc.aws_internet_gateway.this", "module.vpc.aws_route_table.public", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"]}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_route", "name": "public_internet_gateway_ipv6", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_route_table", "name": "database", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_route_table", "name": "elasticache", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_route_table", "name": "intra", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_route_table", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:route-table/rtb-05dac9ab61d8c626e", "id": "rtb-05dac9ab61d8c626e", "owner_id": "************", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "nat-0e8c6f035d7e4f59d", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Environment": "dev", "Name": "az-eks-cluster-vpc-private", "Project": "eks-loki", "Terraform": "true"}, "tags_all": {"Environment": "dev", "Name": "az-eks-cluster-vpc-private", "Project": "eks-loki", "Terraform": "true"}, "timeouts": null, "vpc_id": "vpc-0b14542ee3253a18b"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"]}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_route_table", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:route-table/rtb-0b7394f1a99fdc274", "id": "rtb-0b7394f1a99fdc274", "owner_id": "************", "propagating_vgws": [], "route": [{"carrier_gateway_id": "", "cidr_block": "0.0.0.0/0", "core_network_arn": "", "destination_prefix_list_id": "", "egress_only_gateway_id": "", "gateway_id": "igw-0805fa453303fb8b6", "ipv6_cidr_block": "", "local_gateway_id": "", "nat_gateway_id": "", "network_interface_id": "", "transit_gateway_id": "", "vpc_endpoint_id": "", "vpc_peering_connection_id": ""}], "tags": {"Environment": "dev", "Name": "az-eks-cluster-vpc-public", "Project": "eks-loki", "Terraform": "true"}, "tags_all": {"Environment": "dev", "Name": "az-eks-cluster-vpc-public", "Project": "eks-loki", "Terraform": "true"}, "timeouts": null, "vpc_id": "vpc-0b14542ee3253a18b"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"]}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_route_table", "name": "redshift", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_route_table_association", "name": "database", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_route_table_association", "name": "elasticache", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_route_table_association", "name": "intra", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_route_table_association", "name": "outpost", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_route_table_association", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-09297fadeb621d91b", "route_table_id": "rtb-05dac9ab61d8c626e", "subnet_id": "subnet-08b224a511f12922c", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_route_table.private", "module.vpc.aws_subnet.private", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"]}, {"index_key": 1, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-064c3d7f155627f8b", "route_table_id": "rtb-05dac9ab61d8c626e", "subnet_id": "subnet-0d2d62e0364eb29ef", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_route_table.private", "module.vpc.aws_subnet.private", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"]}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_route_table_association", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-03fe5df1383b297b3", "route_table_id": "rtb-0b7394f1a99fdc274", "subnet_id": "subnet-0c0e13d2a1c174483", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_route_table.public", "module.vpc.aws_subnet.public", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"]}, {"index_key": 1, "schema_version": 0, "attributes": {"gateway_id": "", "id": "rtbassoc-057034de5a83743a8", "route_table_id": "rtb-0b7394f1a99fdc274", "subnet_id": "subnet-0aa66c027fb4d1cb8", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjozMDAwMDAwMDAwMDAsImRlbGV0ZSI6MzAwMDAwMDAwMDAwLCJ1cGRhdGUiOjEyMDAwMDAwMDAwMH19", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_route_table.public", "module.vpc.aws_subnet.public", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"]}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_route_table_association", "name": "redshift", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_route_table_association", "name": "redshift_public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_subnet", "name": "database", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_subnet", "name": "elasticache", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_subnet", "name": "intra", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_subnet", "name": "outpost", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_subnet", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-08b224a511f12922c", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1a", "availability_zone_id": "use1-az1", "cidr_block": "10.0.0.0/20", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-08b224a511f12922c", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "dev", "Name": "az-eks-cluster-vpc-private-us-east-1a", "Project": "eks-loki", "Terraform": "true", "kubernetes.io/role/internal-elb": "1"}, "tags_all": {"Environment": "dev", "Name": "az-eks-cluster-vpc-private-us-east-1a", "Project": "eks-loki", "Terraform": "true", "kubernetes.io/role/internal-elb": "1"}, "timeouts": null, "vpc_id": "vpc-0b14542ee3253a18b"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"], "create_before_destroy": true}, {"index_key": 1, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-0d2d62e0364eb29ef", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1b", "availability_zone_id": "use1-az2", "cidr_block": "*********/20", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0d2d62e0364eb29ef", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "dev", "Name": "az-eks-cluster-vpc-private-us-east-1b", "Project": "eks-loki", "Terraform": "true", "kubernetes.io/role/internal-elb": "1"}, "tags_all": {"Environment": "dev", "Name": "az-eks-cluster-vpc-private-us-east-1b", "Project": "eks-loki", "Terraform": "true", "kubernetes.io/role/internal-elb": "1"}, "timeouts": null, "vpc_id": "vpc-0b14542ee3253a18b"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"], "create_before_destroy": true}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_subnet", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-0c0e13d2a1c174483", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1a", "availability_zone_id": "use1-az1", "cidr_block": "*********/20", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0c0e13d2a1c174483", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "dev", "Name": "az-eks-cluster-vpc-public-us-east-1a", "Project": "eks-loki", "Terraform": "true", "kubernetes.io/role/elb": "1"}, "tags_all": {"Environment": "dev", "Name": "az-eks-cluster-vpc-public-us-east-1a", "Project": "eks-loki", "Terraform": "true", "kubernetes.io/role/elb": "1"}, "timeouts": null, "vpc_id": "vpc-0b14542ee3253a18b"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"]}, {"index_key": 1, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:subnet/subnet-0aa66c027fb4d1cb8", "assign_ipv6_address_on_creation": false, "availability_zone": "us-east-1b", "availability_zone_id": "use1-az2", "cidr_block": "*********/20", "customer_owned_ipv4_pool": "", "enable_dns64": false, "enable_lni_at_device_index": 0, "enable_resource_name_dns_a_record_on_launch": false, "enable_resource_name_dns_aaaa_record_on_launch": false, "id": "subnet-0aa66c027fb4d1cb8", "ipv6_cidr_block": "", "ipv6_cidr_block_association_id": "", "ipv6_native": false, "map_customer_owned_ip_on_launch": false, "map_public_ip_on_launch": false, "outpost_arn": "", "owner_id": "************", "private_dns_hostname_type_on_launch": "ip-name", "tags": {"Environment": "dev", "Name": "az-eks-cluster-vpc-public-us-east-1b", "Project": "eks-loki", "Terraform": "true", "kubernetes.io/role/elb": "1"}, "tags_all": {"Environment": "dev", "Name": "az-eks-cluster-vpc-public-us-east-1b", "Project": "eks-loki", "Terraform": "true", "kubernetes.io/role/elb": "1"}, "timeouts": null, "vpc_id": "vpc-0b14542ee3253a18b"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjo2MDAwMDAwMDAwMDAsImRlbGV0ZSI6MTIwMDAwMDAwMDAwMH0sInNjaGVtYV92ZXJzaW9uIjoiMSJ9", "dependencies": ["data.aws_availability_zones.available", "module.vpc.aws_vpc.this", "module.vpc.aws_vpc_ipv4_cidr_block_association.this"]}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_subnet", "name": "redshift", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_vpc", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 1, "attributes": {"arn": "arn:aws:ec2:us-east-1:************:vpc/vpc-0b14542ee3253a18b", "assign_generated_ipv6_cidr_block": false, "cidr_block": "10.0.0.0/16", "default_network_acl_id": "acl-0d21469bc2e71612d", "default_route_table_id": "rtb-0fa875a9301506986", "default_security_group_id": "sg-06fc7e07b18761f63", "dhcp_options_id": "dopt-0191054b044ac1c99", "enable_dns_hostnames": true, "enable_dns_support": true, "enable_network_address_usage_metrics": false, "id": "vpc-0b14542ee3253a18b", "instance_tenancy": "default", "ipv4_ipam_pool_id": null, "ipv4_netmask_length": null, "ipv6_association_id": "", "ipv6_cidr_block": "", "ipv6_cidr_block_network_border_group": "", "ipv6_ipam_pool_id": "", "ipv6_netmask_length": 0, "main_route_table_id": "rtb-0fa875a9301506986", "owner_id": "************", "tags": {"Environment": "dev", "Name": "az-eks-cluster-vpc", "Project": "eks-loki", "Terraform": "true"}, "tags_all": {"Environment": "dev", "Name": "az-eks-cluster-vpc", "Project": "eks-loki", "Terraform": "true"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJzY2hlbWFfdmVyc2lvbiI6IjEifQ==", "create_before_destroy": true}]}, {"module": "module.vpc", "mode": "managed", "type": "aws_vpc_dhcp_options", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_vpc_dhcp_options_association", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_vpc_ipv4_cidr_block_association", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_vpn_gateway", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_vpn_gateway_attachment", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_vpn_gateway_route_propagation", "name": "intra", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_vpn_gateway_route_propagation", "name": "private", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_vpn_gateway_route_propagation", "name": "public", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}], "check_results": null}