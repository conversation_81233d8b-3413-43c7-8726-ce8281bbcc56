{"version": 4, "terraform_version": "1.12.1", "serial": 39, "lineage": "1d76c1a2-0e60-5f6e-6b1b-100b75616bc8", "outputs": {"cluster_iam_role_arn": {"value": "arn:aws:iam::************:role/az-eks-cluster-cluster-20250604162255995500000001", "type": "string"}, "cluster_iam_role_name": {"value": "az-eks-cluster-cluster-20250604162255995500000001", "type": "string"}, "cluster_name": {"value": "az-eks-cluster", "type": "string"}, "kubectl_config_command": {"value": "aws eks update-kubeconfig --name az-eks-cluster --region us-east-1", "type": "string"}}, "resources": [{"mode": "data", "type": "aws_ami", "name": "eks", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"architecture": "x86_64", "arn": "arn:aws:ec2:us-east-1::image/ami-03bc317f1e0c7d6c1", "block_device_mappings": [{"device_name": "/dev/xvda", "ebs": {"delete_on_termination": "true", "encrypted": "false", "iops": "0", "snapshot_id": "snap-0f919d0c86e9bbf81", "throughput": "0", "volume_initialization_rate": "0", "volume_size": "20", "volume_type": "gp2"}, "no_device": "", "virtual_name": ""}], "boot_mode": "", "creation_date": "2025-05-20T17:09:11.000Z", "deprecation_time": "2027-05-20T17:09:11.000Z", "description": "EKS Kubernetes Worker AMI with AmazonLinux2 image, (k8s: 1.31.7, containerd: 1.7.*)", "ena_support": true, "executable_users": null, "filter": [{"name": "architecture", "values": ["x86_64"]}, {"name": "name", "values": ["amazon-eks-node-1.31-v*"]}, {"name": "virtualization-type", "values": ["hvm"]}], "hypervisor": "xen", "id": "ami-03bc317f1e0c7d6c1", "image_id": "ami-03bc317f1e0c7d6c1", "image_location": "amazon/amazon-eks-node-1.31-v20250519", "image_owner_alias": "amazon", "image_type": "machine", "imds_support": "", "include_deprecated": false, "kernel_id": "", "last_launched_time": "", "most_recent": true, "name": "amazon-eks-node-1.31-v20250519", "name_regex": null, "owner_id": "602401143452", "owners": ["amazon"], "platform": "", "platform_details": "Linux/UNIX", "product_codes": [], "public": true, "ramdisk_id": "", "root_device_name": "/dev/xvda", "root_device_type": "ebs", "root_snapshot_id": "snap-0f919d0c86e9bbf81", "sriov_net_support": "simple", "state": "available", "state_reason": {"code": "UNSET", "message": "UNSET"}, "tags": {}, "timeouts": null, "tpm_support": "", "uefi_data": null, "usage_operation": "RunInstances", "virtualization_type": "hvm"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"mode": "data", "type": "aws_availability_zones", "name": "available", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"all_availability_zones": null, "exclude_names": null, "exclude_zone_ids": null, "filter": null, "group_names": ["us-east-1-zg-1"], "id": "us-east-1", "names": ["us-east-1a", "us-east-1b", "us-east-1c", "us-east-1d", "us-east-1e", "us-east-1f"], "state": "available", "timeouts": null, "zone_ids": ["use1-az1", "use1-az2", "use1-az4", "use1-az6", "use1-az3", "use1-az5"]}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.eks", "mode": "data", "type": "aws_caller_identity", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"account_id": "************", "arn": "arn:aws:iam::************:user/azni_ce9", "id": "************", "user_id": "AIDATXF4JQPHQNHQA2SKB"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.eks", "mode": "data", "type": "aws_iam_policy_document", "name": "assume_role_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Sid\": \"EKSClusterAssumeRole\",\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"sts:TagSession\",\n        \"sts:AssumeRole\"\n      ],\n      \"Principal\": {\n        \"Service\": \"eks.amazonaws.com\"\n      }\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Sid\":\"EKSClusterAssumeRole\",\"Effect\":\"Allow\",\"Action\":[\"sts:TagSession\",\"sts:AssumeRole\"],\"Principal\":{\"Service\":\"eks.amazonaws.com\"}}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["sts:<PERSON><PERSON>Role", "sts:TagSession"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["eks.amazonaws.com"], "type": "Service"}], "resources": [], "sid": "EKSClusterAssumeRole"}], "version": "2012-10-17"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.eks", "mode": "data", "type": "aws_iam_policy_document", "name": "cni_ipv6_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.eks", "mode": "data", "type": "aws_iam_policy_document", "name": "custom", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"id": "513122117", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Sid\": \"Compute\",\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"ec2:RunInstances\",\n        \"ec2:CreateLaunchTemplate\",\n        \"ec2:CreateFleet\"\n      ],\n      \"Resource\": \"*\",\n      \"Condition\": {\n        \"StringEquals\": {\n          \"aws:RequestTag/eks:eks-cluster-name\": \"${aws:PrincipalTag/eks:eks-cluster-name}\"\n        },\n        \"StringLike\": {\n          \"aws:RequestTag/eks:kubernetes-node-class-name\": \"*\",\n          \"aws:RequestTag/eks:kubernetes-node-pool-name\": \"*\"\n        }\n      }\n    },\n    {\n      \"Sid\": \"Storage\",\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"ec2:CreateVolume\",\n        \"ec2:CreateSnapshot\"\n      ],\n      \"Resource\": [\n        \"arn:aws:ec2:*:*:volume/*\",\n        \"arn:aws:ec2:*:*:snapshot/*\"\n      ],\n      \"Condition\": {\n        \"StringEquals\": {\n          \"aws:RequestTag/eks:eks-cluster-name\": \"${aws:PrincipalTag/eks:eks-cluster-name}\"\n        }\n      }\n    },\n    {\n      \"Sid\": \"Networking\",\n      \"Effect\": \"Allow\",\n      \"Action\": \"ec2:CreateNetworkInterface\",\n      \"Resource\": \"*\",\n      \"Condition\": {\n        \"StringEquals\": {\n          \"aws:RequestTag/eks:eks-cluster-name\": \"${aws:PrincipalTag/eks:eks-cluster-name}\",\n          \"aws:RequestTag/eks:kubernetes-cni-node-name\": \"*\"\n        }\n      }\n    },\n    {\n      \"Sid\": \"LoadBalancer\",\n      \"Effect\": \"Allow\",\n      \"Action\": [\n        \"elasticloadbalancing:CreateTargetGroup\",\n        \"elasticloadbalancing:CreateRule\",\n        \"elasticloadbalancing:CreateLoadBalancer\",\n        \"elasticloadbalancing:CreateListener\",\n        \"ec2:CreateSecurityGroup\"\n      ],\n      \"Resource\": \"*\",\n      \"Condition\": {\n        \"StringEquals\": {\n          \"aws:RequestTag/eks:eks-cluster-name\": \"${aws:PrincipalTag/eks:eks-cluster-name}\"\n        }\n      }\n    },\n    {\n      \"Sid\": \"ShieldProtection\",\n      \"Effect\": \"Allow\",\n      \"Action\": \"shield:CreateProtection\",\n      \"Resource\": \"*\",\n      \"Condition\": {\n        \"StringEquals\": {\n          \"aws:RequestTag/eks:eks-cluster-name\": \"${aws:PrincipalTag/eks:eks-cluster-name}\"\n        }\n      }\n    },\n    {\n      \"Sid\": \"ShieldTagResource\",\n      \"Effect\": \"Allow\",\n      \"Action\": \"shield:TagResource\",\n      \"Resource\": \"arn:aws:shield::*:protection/*\",\n      \"Condition\": {\n        \"StringEquals\": {\n          \"aws:RequestTag/eks:eks-cluster-name\": \"${aws:PrincipalTag/eks:eks-cluster-name}\"\n        }\n      }\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Sid\":\"Compute\",\"Effect\":\"Allow\",\"Action\":[\"ec2:RunInstances\",\"ec2:CreateLaunchTemplate\",\"ec2:CreateFleet\"],\"Resource\":\"*\",\"Condition\":{\"StringEquals\":{\"aws:RequestTag/eks:eks-cluster-name\":\"${aws:PrincipalTag/eks:eks-cluster-name}\"},\"StringLike\":{\"aws:RequestTag/eks:kubernetes-node-class-name\":\"*\",\"aws:RequestTag/eks:kubernetes-node-pool-name\":\"*\"}}},{\"Sid\":\"Storage\",\"Effect\":\"Allow\",\"Action\":[\"ec2:CreateVolume\",\"ec2:CreateSnapshot\"],\"Resource\":[\"arn:aws:ec2:*:*:volume/*\",\"arn:aws:ec2:*:*:snapshot/*\"],\"Condition\":{\"StringEquals\":{\"aws:RequestTag/eks:eks-cluster-name\":\"${aws:PrincipalTag/eks:eks-cluster-name}\"}}},{\"Sid\":\"Networking\",\"Effect\":\"Allow\",\"Action\":\"ec2:CreateNetworkInterface\",\"Resource\":\"*\",\"Condition\":{\"StringEquals\":{\"aws:RequestTag/eks:eks-cluster-name\":\"${aws:PrincipalTag/eks:eks-cluster-name}\",\"aws:RequestTag/eks:kubernetes-cni-node-name\":\"*\"}}},{\"Sid\":\"LoadBalancer\",\"Effect\":\"Allow\",\"Action\":[\"elasticloadbalancing:CreateTargetGroup\",\"elasticloadbalancing:CreateRule\",\"elasticloadbalancing:CreateLoadBalancer\",\"elasticloadbalancing:CreateListener\",\"ec2:CreateSecurityGroup\"],\"Resource\":\"*\",\"Condition\":{\"StringEquals\":{\"aws:RequestTag/eks:eks-cluster-name\":\"${aws:PrincipalTag/eks:eks-cluster-name}\"}}},{\"Sid\":\"ShieldProtection\",\"Effect\":\"Allow\",\"Action\":\"shield:CreateProtection\",\"Resource\":\"*\",\"Condition\":{\"StringEquals\":{\"aws:RequestTag/eks:eks-cluster-name\":\"${aws:PrincipalTag/eks:eks-cluster-name}\"}}},{\"Sid\":\"ShieldTagResource\",\"Effect\":\"Allow\",\"Action\":\"shield:TagResource\",\"Resource\":\"arn:aws:shield::*:protection/*\",\"Condition\":{\"StringEquals\":{\"aws:RequestTag/eks:eks-cluster-name\":\"${aws:PrincipalTag/eks:eks-cluster-name}\"}}}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["ec2:<PERSON><PERSON><PERSON><PERSON><PERSON>", "ec2:CreateLaunchTemplate", "ec2:RunInstances"], "condition": [{"test": "StringEquals", "values": ["${aws:PrincipalTag/eks:eks-cluster-name}"], "variable": "aws:RequestTag/eks:eks-cluster-name"}, {"test": "StringLike", "values": ["*"], "variable": "aws:RequestTag/eks:kubernetes-node-class-name"}, {"test": "StringLike", "values": ["*"], "variable": "aws:RequestTag/eks:kubernetes-node-pool-name"}], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["*"], "sid": "Compute"}, {"actions": ["ec2:CreateSnapshot", "ec2:CreateVolume"], "condition": [{"test": "StringEquals", "values": ["${aws:PrincipalTag/eks:eks-cluster-name}"], "variable": "aws:RequestTag/eks:eks-cluster-name"}], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:ec2:*:*:snapshot/*", "arn:aws:ec2:*:*:volume/*"], "sid": "Storage"}, {"actions": ["ec2:CreateNetworkInterface"], "condition": [{"test": "StringEquals", "values": ["${aws:PrincipalTag/eks:eks-cluster-name}"], "variable": "aws:RequestTag/eks:eks-cluster-name"}, {"test": "StringEquals", "values": ["*"], "variable": "aws:RequestTag/eks:kubernetes-cni-node-name"}], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["*"], "sid": "Networking"}, {"actions": ["ec2:CreateSecurityGroup", "elasticloadbalancing:CreateListener", "elasticloadbalancing:CreateLoadBalancer", "elasticloadbalancing:CreateRule", "elasticloadbalancing:CreateTargetGroup"], "condition": [{"test": "StringEquals", "values": ["${aws:PrincipalTag/eks:eks-cluster-name}"], "variable": "aws:RequestTag/eks:eks-cluster-name"}], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["*"], "sid": "LoadBalancer"}, {"actions": ["shield:CreateProtection"], "condition": [{"test": "StringEquals", "values": ["${aws:PrincipalTag/eks:eks-cluster-name}"], "variable": "aws:RequestTag/eks:eks-cluster-name"}], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["*"], "sid": "ShieldProtection"}, {"actions": ["shield:TagResource"], "condition": [{"test": "StringEquals", "values": ["${aws:PrincipalTag/eks:eks-cluster-name}"], "variable": "aws:RequestTag/eks:eks-cluster-name"}], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [], "resources": ["arn:aws:shield::*:protection/*"], "sid": "ShieldTagResource"}], "version": "2012-10-17"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.eks", "mode": "data", "type": "aws_iam_policy_document", "name": "node_assume_role_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.eks", "mode": "data", "type": "aws_iam_session_context", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:user/azni_ce9", "id": "arn:aws:iam::************:user/azni_ce9", "issuer_arn": "arn:aws:iam::************:user/azni_ce9", "issuer_id": "", "issuer_name": "", "session_name": ""}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.eks", "mode": "data", "type": "aws_partition", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"dns_suffix": "amazonaws.com", "id": "aws", "partition": "aws", "reverse_dns_prefix": "com.amazonaws"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.eks", "mode": "managed", "type": "aws_iam_policy", "name": "cni_ipv6_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.eks", "mode": "managed", "type": "aws_iam_policy", "name": "custom", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:policy/az-eks-cluster-cluster-20250604162255996600000002", "attachment_count": 0, "description": "", "id": "arn:aws:iam::************:policy/az-eks-cluster-cluster-20250604162255996600000002", "name": "az-eks-cluster-cluster-20250604162255996600000002", "name_prefix": "az-eks-cluster-cluster-", "path": "/", "policy": "{\"Statement\":[{\"Action\":[\"ec2:RunInstances\",\"ec2:CreateLaunchTemplate\",\"ec2:CreateFleet\"],\"Condition\":{\"StringEquals\":{\"aws:RequestTag/eks:eks-cluster-name\":\"${aws:PrincipalTag/eks:eks-cluster-name}\"},\"StringLike\":{\"aws:RequestTag/eks:kubernetes-node-class-name\":\"*\",\"aws:RequestTag/eks:kubernetes-node-pool-name\":\"*\"}},\"Effect\":\"Allow\",\"Resource\":\"*\",\"Sid\":\"Compute\"},{\"Action\":[\"ec2:CreateVolume\",\"ec2:CreateSnapshot\"],\"Condition\":{\"StringEquals\":{\"aws:RequestTag/eks:eks-cluster-name\":\"${aws:PrincipalTag/eks:eks-cluster-name}\"}},\"Effect\":\"Allow\",\"Resource\":[\"arn:aws:ec2:*:*:volume/*\",\"arn:aws:ec2:*:*:snapshot/*\"],\"Sid\":\"Storage\"},{\"Action\":\"ec2:CreateNetworkInterface\",\"Condition\":{\"StringEquals\":{\"aws:RequestTag/eks:eks-cluster-name\":\"${aws:PrincipalTag/eks:eks-cluster-name}\",\"aws:RequestTag/eks:kubernetes-cni-node-name\":\"*\"}},\"Effect\":\"Allow\",\"Resource\":\"*\",\"Sid\":\"Networking\"},{\"Action\":[\"elasticloadbalancing:CreateTargetGroup\",\"elasticloadbalancing:CreateRule\",\"elasticloadbalancing:CreateLoadBalancer\",\"elasticloadbalancing:CreateListener\",\"ec2:CreateSecurityGroup\"],\"Condition\":{\"StringEquals\":{\"aws:RequestTag/eks:eks-cluster-name\":\"${aws:PrincipalTag/eks:eks-cluster-name}\"}},\"Effect\":\"Allow\",\"Resource\":\"*\",\"Sid\":\"LoadBalancer\"},{\"Action\":\"shield:CreateProtection\",\"Condition\":{\"StringEquals\":{\"aws:RequestTag/eks:eks-cluster-name\":\"${aws:PrincipalTag/eks:eks-cluster-name}\"}},\"Effect\":\"Allow\",\"Resource\":\"*\",\"Sid\":\"ShieldProtection\"},{\"Action\":\"shield:TagResource\",\"Condition\":{\"StringEquals\":{\"aws:RequestTag/eks:eks-cluster-name\":\"${aws:PrincipalTag/eks:eks-cluster-name}\"}},\"Effect\":\"Allow\",\"Resource\":\"arn:aws:shield::*:protection/*\",\"Sid\":\"ShieldTagResource\"}],\"Version\":\"2012-10-17\"}", "policy_id": "ANPATXF4JQPHXQFSNIVVZ", "tags": {"Environment": "dev", "Project": "eks-grafana", "Terraform": "true"}, "tags_all": {"Environment": "dev", "Project": "eks-grafana", "Terraform": "true"}}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.eks.data.aws_iam_policy_document.custom", "module.eks.data.aws_partition.current"], "create_before_destroy": true}]}, {"module": "module.eks", "mode": "managed", "type": "aws_iam_role", "name": "eks_auto", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.eks", "mode": "managed", "type": "aws_iam_role", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"arn": "arn:aws:iam::************:role/az-eks-cluster-cluster-20250604162255995500000001", "assume_role_policy": "{\"Statement\":[{\"Action\":[\"sts:TagSession\",\"sts:AssumeRole\"],\"Effect\":\"Allow\",\"Principal\":{\"Service\":\"eks.amazonaws.com\"},\"Sid\":\"EKSClusterAssumeRole\"}],\"Version\":\"2012-10-17\"}", "create_date": "2025-06-04T16:22:57Z", "description": "", "force_detach_policies": true, "id": "az-eks-cluster-cluster-20250604162255995500000001", "inline_policy": [], "managed_policy_arns": [], "max_session_duration": 3600, "name": "az-eks-cluster-cluster-20250604162255995500000001", "name_prefix": "az-eks-cluster-cluster-", "path": "/", "permissions_boundary": "", "tags": {"Environment": "dev", "Project": "eks-grafana", "Terraform": "true"}, "tags_all": {"Environment": "dev", "Project": "eks-grafana", "Terraform": "true"}, "unique_id": "AROATXF4JQPH52WMITNMW"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.eks.data.aws_iam_policy_document.assume_role_policy"], "create_before_destroy": true}]}, {"module": "module.eks", "mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "additional", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.eks", "mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "custom", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"id": "az-eks-cluster-cluster-20250604162255995500000001-20250604162258624200000004", "policy_arn": "arn:aws:iam::************:policy/az-eks-cluster-cluster-20250604162255996600000002", "role": "az-eks-cluster-cluster-20250604162255995500000001"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.eks.aws_iam_policy.custom", "module.eks.aws_iam_role.this", "module.eks.data.aws_iam_policy_document.assume_role_policy", "module.eks.data.aws_iam_policy_document.custom", "module.eks.data.aws_partition.current"], "create_before_destroy": true}]}, {"module": "module.eks", "mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "eks_auto", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.eks", "mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "eks_auto_additional", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.eks", "mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": "AmazonEKSClusterPolicy", "schema_version": 0, "attributes": {"id": "az-eks-cluster-cluster-20250604162255995500000001-20250604162258624200000003", "policy_arn": "arn:aws:iam::aws:policy/AmazonEKSClusterPolicy", "role": "az-eks-cluster-cluster-20250604162255995500000001"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.eks.aws_iam_role.this", "module.eks.data.aws_iam_policy_document.assume_role_policy", "module.eks.data.aws_partition.current"], "create_before_destroy": true}, {"index_key": "AmazonEKSVPCResourceController", "schema_version": 0, "attributes": {"id": "az-eks-cluster-cluster-20250604162255995500000001-20250604162258624500000005", "policy_arn": "arn:aws:iam::aws:policy/AmazonEKSVPCResourceController", "role": "az-eks-cluster-cluster-20250604162255995500000001"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.eks.aws_iam_role.this", "module.eks.data.aws_iam_policy_document.assume_role_policy", "module.eks.data.aws_partition.current"], "create_before_destroy": true}]}, {"module": "module.eks.module.eks_managed_node_group[\"general\"]", "mode": "data", "type": "aws_caller_identity", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "************", "arn": "arn:aws:iam::************:user/azni_ce9", "id": "************", "user_id": "AIDATXF4JQPHQNHQA2SKB"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.eks.module.eks_managed_node_group[\"general\"]", "mode": "data", "type": "aws_iam_policy_document", "name": "assume_role_policy", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"id": "**********", "json": "{\n  \"Version\": \"2012-10-17\",\n  \"Statement\": [\n    {\n      \"Sid\": \"EKSNodeAssumeRole\",\n      \"Effect\": \"Allow\",\n      \"Action\": \"sts:AssumeRole\",\n      \"Principal\": {\n        \"Service\": \"ec2.amazonaws.com\"\n      }\n    }\n  ]\n}", "minified_json": "{\"Version\":\"2012-10-17\",\"Statement\":[{\"Sid\":\"EKSNodeAssumeRole\",\"Effect\":\"Allow\",\"Action\":\"sts:AssumeRole\",\"Principal\":{\"Service\":\"ec2.amazonaws.com\"}}]}", "override_json": null, "override_policy_documents": null, "policy_id": null, "source_json": null, "source_policy_documents": null, "statement": [{"actions": ["sts:<PERSON><PERSON>Role"], "condition": [], "effect": "Allow", "not_actions": [], "not_principals": [], "not_resources": [], "principals": [{"identifiers": ["ec2.amazonaws.com"], "type": "Service"}], "resources": [], "sid": "EKSNodeAssumeRole"}], "version": "2012-10-17"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.eks.module.eks_managed_node_group[\"general\"]", "mode": "data", "type": "aws_partition", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"schema_version": 0, "attributes": {"dns_suffix": "amazonaws.com", "id": "aws", "partition": "aws", "reverse_dns_prefix": "com.amazonaws"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.eks.module.kms", "mode": "data", "type": "aws_caller_identity", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"account_id": "************", "arn": "arn:aws:iam::************:user/azni_ce9", "id": "************", "user_id": "AIDATXF4JQPHQNHQA2SKB"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.eks.module.kms", "mode": "data", "type": "aws_partition", "name": "current", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": [{"index_key": 0, "schema_version": 0, "attributes": {"dns_suffix": "amazonaws.com", "id": "aws", "partition": "aws", "reverse_dns_prefix": "com.amazonaws"}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.vpc", "mode": "data", "type": "aws_iam_policy_document", "name": "flow_log_cloudwatch_assume_role", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "data", "type": "aws_iam_policy_document", "name": "vpc_flow_log_cloudwatch", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_customer_gateway", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_default_vpc", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_iam_policy", "name": "vpc_flow_log_cloudwatch", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_iam_role", "name": "vpc_flow_log_cloudwatch", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_iam_role_policy_attachment", "name": "vpc_flow_log_cloudwatch", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}, {"module": "module.vpc", "mode": "managed", "type": "aws_vpc_dhcp_options", "name": "this", "provider": "provider[\"registry.terraform.io/hashicorp/aws\"]", "instances": []}], "check_results": [{"object_kind": "var", "config_addr": "module.eks.module.self_managed_node_group.var.platform", "status": "pass", "objects": null}, {"object_kind": "resource", "config_addr": "module.eks.module.self_managed_node_group.module.user_data.null_resource.validate_cluster_service_cidr", "status": "pass", "objects": null}, {"object_kind": "resource", "config_addr": "module.eks.module.eks_managed_node_group.module.user_data.null_resource.validate_cluster_service_cidr", "status": "unknown", "objects": [{"object_addr": "module.eks.module.eks_managed_node_group[\"general\"].module.user_data.null_resource.validate_cluster_service_cidr", "status": "unknown"}]}]}