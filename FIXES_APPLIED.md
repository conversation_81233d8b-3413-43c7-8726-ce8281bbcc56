# Terraform EKS Issues - Fixes Applied

## Issues Resolved

### 1. AMI Query Error ✅
**Problem**: `Your query returned no results` for EKS 1.32 AMIs
**Root Cause**: Kubernetes 1.32 AMIs are not yet available in AWS
**Solution**: 
- Updated `variables.tf`: Changed default cluster_version from "1.32" to "1.31"
- Updated `main.tf`: Changed AMI filter from "amazon-eks-node-1.32-v*" to "amazon-eks-node-1.31-v*"

### 2. IAM Role Deprecation Warning ✅
**Problem**: `inline_policy is deprecated` warning from EKS module
**Root Cause**: Using older EKS module version (19.x) with deprecated IAM features
**Solution**:
- Updated `main.tf`: Upgraded EKS module version from "~> 19.0" to "~> 20.0"
- Removed deprecated `AmazonEKSServicePolicy` attachment from `iam.tf`

## Files Modified

1. **variables.tf**
   - Line 16: `default = "1.32"` → `default = "1.31"`

2. **main.tf**
   - Line 38: `values = ["amazon-eks-node-1.32-v*"]` → `values = ["amazon-eks-node-1.31-v*"]`
   - Line 54: `version = "~> 19.0"` → `version = "~> 20.0"`

3. **iam.tf**
   - Removed lines 27-30: Deprecated `aws_iam_role_policy_attachment.eks_service_policy`

## Verification

✅ `terraform init` - Successfully downloaded EKS module v20.36.0
✅ `terraform plan` - No errors, shows 7 to add, 4 to change, 3 to destroy

## Ready to Deploy

You can now run:
```bash
terraform apply
```

The plan will:
- Deploy monitoring stack (Loki, Grafana, Promtail)
- Update EKS cluster to use newer module version
- Remove deprecated IAM policies
- Create necessary resources for the logging infrastructure
