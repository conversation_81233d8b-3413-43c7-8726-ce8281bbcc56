name: Release

on:
  workflow_dispatch:
  push:
    branches:
      - main
      - master
    paths:
      - '**/*.tpl'
      - '**/*.py'
      - '**/*.tf'
      - '.github/workflows/release.yml'

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    # Skip running release workflow on forks
    if: github.repository_owner == 'terraform-aws-modules'
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          persist-credentials: false
          fetch-depth: 0

      - name: Release
        uses: cycjimmy/semantic-release-action@v3
        with:
          semantic_version: 18.0.0
          extra_plugins: |
            @semantic-release/changelog@6.0.0
            @semantic-release/git@10.0.0
            conventional-changelog-conventionalcommits@4.6.3
        env:
          GITHUB_TOKEN: ${{ secrets.SEMANTIC_RELEASE_TOKEN }}
