name: Pre-Commit

on:
  pull_request:
    branches:
      - main
      - master

env:
  TERRAFORM_DOCS_VERSION: v0.16.0
  TFLINT_VERSION: v0.44.1

jobs:
  collectInputs:
    name: Collect workflow inputs
    runs-on: ubuntu-latest
    outputs:
      directories: ${{ steps.dirs.outputs.directories }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Get root directories
        id: dirs
        uses: clowdhaus/terraform-composite-actions/directories@v1.8.3

  preCommitMinVersions:
    name: Min TF pre-commit
    needs: collectInputs
    runs-on: ubuntu-latest
    strategy:
      matrix:
        directory: ${{ fromJson(needs.collectInputs.outputs.directories) }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Terraform min/max versions
        id: minMax
        uses: clowdhaus/terraform-min-max@v1.2.4
        with:
          directory: ${{ matrix.directory }}

      - name: Pre-commit Terraform ${{ steps.minMax.outputs.minVersion }}
        # Run only validate pre-commit check on min version supported
        if: ${{ matrix.directory !=  '.' }}
        uses: clowdhaus/terraform-composite-actions/pre-commit@v1.8.3
        with:
          terraform-version: ${{ steps.minMax.outputs.minVersion }}
          tflint-version: ${{ env.TFLINT_VERSION }}
          args: 'terraform_validate --color=always --show-diff-on-failure --files ${{ matrix.directory }}/*'

      - name: Pre-commit Terraform ${{ steps.minMax.outputs.minVersion }}
        # Run only validate pre-commit check on min version supported
        if: ${{ matrix.directory ==  '.' }}
        uses: clowdhaus/terraform-composite-actions/pre-commit@v1.8.3
        with:
          terraform-version: ${{ steps.minMax.outputs.minVersion }}
          tflint-version: ${{ env.TFLINT_VERSION }}
          args: 'terraform_validate --color=always --show-diff-on-failure --files $(ls *.tf)'

  preCommitMaxVersion:
    name: Max TF pre-commit
    runs-on: ubuntu-latest
    needs: collectInputs
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.ref }}
          repository: ${{github.event.pull_request.head.repo.full_name}}

      - name: Terraform min/max versions
        id: minMax
        uses: clowdhaus/terraform-min-max@v1.2.4

      - name: Pre-commit Terraform ${{ steps.minMax.outputs.maxVersion }}
        uses: clowdhaus/terraform-composite-actions/pre-commit@v1.8.3
        with:
          terraform-version: ${{ steps.minMax.outputs.maxVersion }}
          tflint-version: ${{ env.TFLINT_VERSION }}
          terraform-docs-version: ${{ env.TERRAFORM_DOCS_VERSION }}
          install-hcledit: true
