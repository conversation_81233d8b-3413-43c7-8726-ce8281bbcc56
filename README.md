# EKS Cluster with <PERSON><PERSON> and <PERSON>

This Terraform project deploys an EKS cluster with <PERSON><PERSON> and <PERSON> for log monitoring. The infrastructure includes a VPC with public and private subnets, an EKS cluster with managed node groups, and a complete logging stack with <PERSON><PERSON> and <PERSON>.

## Prerequisites

- AWS CLI configured with appropriate credentials
- Terraform >= 1.0.0
- kubectl
- helm

## Infrastructure Components

- VPC with public/private subnets across 2 AZs
- EKS cluster (Kubernetes 1.27)
- Managed node group with t3.medium instances
- Grafana for visualization
- <PERSON> for log storage
- Promtail for log collection

## Deployment Steps

1. Clone this repository:

   ```bash
   git clone <repository-url>
   cd eks-loki-project
   ```

2. Copy the example variables file and modify as needed:

   ```bash
   cp terraform.tfvars.example terraform.tfvars
   ```

3. Initialize Terraform:

   ```bash
   terraform init
   ```

4. Review the planned changes:

   ```bash
   terraform plan
   ```

5. Apply the configuration:

   ```bash
   terraform apply
   ```

6. After deployment completes, configure kubectl:
   ```bash
   aws eks update-kubeconfig --name <cluster-name> --region <region>
   ```

## Accessing Grafana

1. Get the Grafana URL from the Terraform outputs:

   ```bash
   terraform output grafana_url
   ```

2. Access Grafana using the URL in your browser
   - Username: admin
   - Password: (value of grafana_admin_password from terraform.tfvars)

## Using Log Queries

The following log queries are pre-configured:

1. View all logs from the past hour:

   ```
   {namespace!=""} |= "" | __error__ = ""
   ```

2. View info-level logs:
   ```
   {namespace!=""} |= "level=info" or {namespace!=""} |= "severity=info" or {namespace!=""} |= "INFO"
   ```

## Generating Test Logs

To generate test logs, you can deploy a sample application:

```bash
kubectl create namespace test
kubectl run test-app --image=nginx -n test
```

The logs will be automatically collected by Promtail and available in Grafana.

## Cleanup

To destroy all resources:

```bash
terraform destroy
```

## Security Notes

- The default Grafana admin password is set to "admin". Change this in production.
- The EKS cluster endpoint is publicly accessible. Consider restricting access in production.
- Review and adjust security groups and IAM roles based on your security requirements.

## Troubleshooting

1. If Grafana is not accessible:

   - Check if the LoadBalancer service is properly created
   - Verify security group rules allow inbound traffic
   - Check if the pods are running: `kubectl get pods -n monitoring`

2. If logs are not appearing:
   - Verify Promtail is running: `kubectl get pods -n monitoring -l app.kubernetes.io/name=promtail`
   - Check Promtail logs: `kubectl logs -n monitoring -l app.kubernetes.io/name=promtail`
   - Verify Loki is running: `kubectl get pods -n monitoring -l app=loki`

## Contributing

Feel free to submit issues and enhancement requests.
