resource "kubernetes_namespace" "monitoring" {
  metadata {
    name = "monitoring"
    annotations = {
      "helm.sh/resource-policy" = "keep"
    }
  }

  timeouts {
    delete = "30m"
  }

  depends_on = [
    module.eks,
    aws_iam_role.eks_cluster,
    aws_iam_role.eks_node_group,
    null_resource.wait_for_cluster
  ]

  lifecycle {
    create_before_destroy = true
  }
}

# Wait for the cluster to be fully ready before deploying <PERSON><PERSON> charts
resource "null_resource" "wait_for_cluster_ready" {
  depends_on = [null_resource.wait_for_cluster]

  provisioner "local-exec" {
    command = <<-EOT
      echo "Waiting for cluster to be fully ready..."
      kubectl wait --for=condition=ready node --all --timeout=300s
      echo "Cluster nodes are ready!"
      
      # Verify cluster access
      kubectl get nodes
      
      # Check if the monitoring namespace exists
      kubectl get namespace monitoring
      
      # Verify He<PERSON> is working
      helm list -A
    EOT
  }
}





resource "helm_release" "grafana" {
  name       = "grafana"
  repository = "https://grafana.github.io/helm-charts"
  chart      = "grafana"
  namespace  = kubernetes_namespace.monitoring.metadata[0].name
  version    = "6.57.0"
  atomic     = true
  timeout    = 900 # 15 minutes
  wait       = true
  wait_for_jobs = true

  set {
    name  = "persistence.enabled"
    value = "true"
  }

  set {
    name  = "persistence.size"
    value = "10Gi"
  }

  set {
    name  = "adminPassword"
    value = var.grafana_admin_password
  }

  set {
    name  = "service.type"
    value = "LoadBalancer"
  }

  set {
    name  = "resources.requests.memory"
    value = "256Mi"
  }

  set {
    name  = "resources.requests.cpu"
    value = "100m"
  }

  set {
    name  = "resources.limits.memory"
    value = "512Mi"
  }

  set {
    name  = "resources.limits.cpu"
    value = "200m"
  }

  depends_on = [
    kubernetes_namespace.monitoring,
    null_resource.wait_for_cluster_ready
  ]
} 