resource "kubernetes_namespace" "monitoring" {
  metadata {
    name = "monitoring"
    annotations = {
      "helm.sh/resource-policy" = "keep"
    }
  }

  timeouts {
    delete = "30m"
  }

  depends_on = [
    module.eks,
    aws_iam_role.eks_cluster,
    aws_iam_role.eks_node_group,
    null_resource.wait_for_cluster
  ]

  lifecycle {
    create_before_destroy = true
  }
}

# Wait for the cluster to be fully ready before deploying <PERSON><PERSON> charts
resource "null_resource" "wait_for_cluster_ready" {
  depends_on = [null_resource.wait_for_cluster]

  provisioner "local-exec" {
    command = <<-EOT
      echo "Waiting for cluster to be fully ready..."
      kubectl wait --for=condition=ready node --all --timeout=300s
      echo "Cluster nodes are ready!"
      
      # Verify cluster access
      kubectl get nodes
      
      # Check if the monitoring namespace exists
      kubectl get namespace monitoring
      
      # Verify He<PERSON> is working
      helm list -A
    EOT
  }
}

resource "helm_release" "loki" {
  name       = "loki"
  repository = "https://grafana.github.io/helm-charts"
  chart      = "loki"
  namespace  = kubernetes_namespace.monitoring.metadata[0].name
  version    = "5.41.3"
  atomic     = true
  timeout    = 1800 # 30 minutes
  wait       = true
  wait_for_jobs = true

  set {
    name  = "auth_enabled"
    value = "false"
  }

  set {
    name  = "storage.type"
    value = "filesystem"
  }

  set {
    name  = "persistence.enabled"
    value = "false"
  }

  set {
    name  = "loki.limits_config.retention_period"
    value = "744h"
  }

  set {
    name  = "loki.chunk_store_config.max_look_back_period"
    value = "744h"
  }

  set {
    name  = "loki.table_manager.retention_deletes_enabled"
    value = "true"
  }

  set {
    name  = "loki.table_manager.retention_period"
    value = "744h"
  }

  set {
    name  = "resources.requests.memory"
    value = "256Mi"
  }

  set {
    name  = "resources.requests.cpu"
    value = "100m"
  }

  set {
    name  = "resources.limits.memory"
    value = "512Mi"
  }

  set {
    name  = "resources.limits.cpu"
    value = "200m"
  }

  set {
    name  = "loki.read.replicas"
    value = "1"
  }

  set {
    name  = "loki.write.replicas"
    value = "1"
  }

  set {
    name  = "loki.backend.replicas"
    value = "1"
  }

  depends_on = [
    kubernetes_namespace.monitoring,
    null_resource.wait_for_cluster_ready
  ]
}

resource "helm_release" "promtail" {
  name       = "promtail"
  repository = "https://grafana.github.io/helm-charts"
  chart      = "promtail"
  namespace  = kubernetes_namespace.monitoring.metadata[0].name
  version    = "6.15.3"
  atomic     = true
  timeout    = 900 # 15 minutes
  wait       = true
  wait_for_jobs = true

  set {
    name  = "config.lokiAddress"
    value = "http://loki:3100/loki/api/v1/push"
  }

  set {
    name  = "resources.requests.memory"
    value = "128Mi"
  }

  set {
    name  = "resources.requests.cpu"
    value = "100m"
  }

  set {
    name  = "resources.limits.memory"
    value = "256Mi"
  }

  set {
    name  = "resources.limits.cpu"
    value = "200m"
  }

  depends_on = [
    kubernetes_namespace.monitoring,
    helm_release.loki
  ]
}

resource "helm_release" "grafana" {
  name       = "grafana"
  repository = "https://grafana.github.io/helm-charts"
  chart      = "grafana"
  namespace  = kubernetes_namespace.monitoring.metadata[0].name
  version    = "6.57.0"
  atomic     = true
  timeout    = 900 # 15 minutes
  wait       = true
  wait_for_jobs = true

  set {
    name  = "persistence.enabled"
    value = "true"
  }

  set {
    name  = "persistence.size"
    value = "10Gi"
  }

  set {
    name  = "adminPassword"
    value = var.grafana_admin_password
  }

  set {
    name  = "service.type"
    value = "LoadBalancer"
  }

  set {
    name  = "resources.requests.memory"
    value = "256Mi"
  }

  set {
    name  = "resources.requests.cpu"
    value = "100m"
  }

  set {
    name  = "resources.limits.memory"
    value = "512Mi"
  }

  set {
    name  = "resources.limits.cpu"
    value = "200m"
  }

  set {
    name  = "datasources.datasources.yaml.apiVersion"
    value = "1"
  }

  set {
    name  = "datasources.datasources.yaml.datasources[0].name"
    value = "Loki"
  }

  set {
    name  = "datasources.datasources.yaml.datasources[0].type"
    value = "loki"
  }

  set {
    name  = "datasources.datasources.yaml.datasources[0].url"
    value = "http://loki:3100"
  }

  set {
    name  = "datasources.datasources.yaml.datasources[0].access"
    value = "proxy"
  }

  depends_on = [
    kubernetes_namespace.monitoring,
    helm_release.loki
  ]
} 